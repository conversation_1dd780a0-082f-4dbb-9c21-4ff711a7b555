from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.chains import RetrievalQA
from graph_rag.langchain_wrapper import CustomLLM_LangChain,CustomQwenLLM
from langchain.prompts import Chat<PERSON>romptTemplate,PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain_core.messages import HumanMessage, SystemMessage

import networkx as nx  # 用于图的构建和操作
from llama_index.graph_stores.neo4j import Neo4jPropertyGraphStore
from llama_index.core import PropertyGraphIndex
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from graph_rag.GraphRAGQueryEngine import GraphRAG<PERSON>ueryEngine
from graph_rag.GraphRAGStore import GraphRAGStore
from graph_rag.llamaindex_wrapper import CustomLLM,QwenLLM
import concurrent.futures
import json
import os
import time
from config import RAGConfig
# 初始化配置
config = RAGConfig()

# 1.1 嵌入与索引加载
EMBED_MODEL   = config.embedding_model
FAISS_INDEX   = config.vector_store_path

# 原有配置（已注释）
# llm_vec = CustomQwenLLM(
# model_name="qwen2.5-3b-instruct",
# openai_api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
# max_tokens=256,
# temperature=0.3,
# stream=False
# )
# llm = QwenLLM(
# api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
# model="qwen2.5-3b-instruct",
# max_tokens=2048,
# temperature=0.5,
# top_p=0.95,
# presence_penalty=1.03,
# frequency_penalty=1.0,
# streaming=False
# )
# llm_vec = CustomLLM_LangChain(
# model_name="flan-t5-xl",
# base_url="http://localhost:8421",
# temperature=0.5,
# max_tokens=2048,
# presence_penalty=1.03,
# frequency_penalty=1.0,
# top_p=0.95,
# streaming=False,  # 根据需求设置流式输出
# openai_api_key=""   # 如无验证则留空
# )
# llm = CustomLLM(
# base_url="http://localhost:8421",
# model="flan-t5-xl",
# max_tokens=512,
# presence_penalty=1.03,
# frequency_penalty=1.0,
# temperature=0.5,
# top_p=0.92,
# streaming=False,
# )

# 使用硅基流动配置
llm_vec = CustomLLM_LangChain(
    model_name=config.llm_vec_model,
    base_url=config.llm_vec_url,
    temperature=0.5,
    max_tokens=2048,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    top_p=0.95,
    streaming=False,
    openai_api_key=config.llm_vec_api_key
)

llm = CustomLLM(
    base_url=config.llm_detail_url,
    model=config.llm_detail_model,
    max_tokens=512,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    temperature=0.5,
    top_p=0.92,
    streaming=False,
    api_key=config.llm_detail_api_key,
)

# 1. 初始化组件
embeddings = HuggingFaceEmbeddings(
    model_name=EMBED_MODEL,
    model_kwargs={"device": "cuda"}
)
vectorstore = FAISS.load_local(FAISS_INDEX, embeddings, allow_dangerous_deserialization=True)

vec_prompt = PromptTemplate.from_template(
"""
You are an expert in knowledge reasoning question answering. Please reasoning step by step with the Context to answer the question as the QA examples below.
[Examples]
Context Title1: Christopher Nolan
Christopher Edward Nolan ( ; born 30 July 1970) is an English-American film director, producer, and screenwriter. He is one of the highest-grossing directors in history, and among the most successful and acclaimed filmmakers of the 21st century.

Context Title2: Jeremy Theobald
Jeremy Theobald is a British actor best known for his portrayal of "The Young Man", the main character in Christopher Nolan's 1998 major picture debut "Following", and for which Theobald was also a producer, Filming was scheduled around their day jobs. Jonathan Romney, writing in the "New Statesman", noted that "Nolan and his cast are terrific finds: I wouldn't normally say this to struggling artists, but they might want to give up their day jobs."

Q: Jeremy Theobald and Christopher Nolan share what profession?
A: Jeremy Theobald is an actor and producer. Christopher Nolan is a director, producer, and screenwriter. Therefore, they both share the profession of being a producer. So the answer is: producer.

Remember refering to the answer format of the above QA examples and give a brief answer. Make sure to end with "the answer is":
=========
Context:
{context}
=========
Question: {question}
Answer:
"""
)
# 2. 构建检索链
qa_chain = RetrievalQA.from_chain_type(
    llm=llm_vec,
    chain_type="stuff",
    retriever=vectorstore.as_retriever(search_kwargs={"k": 5}),
    chain_type_kwargs={
    "prompt": vec_prompt,   # 覆盖默认 prompt
    #"verbose": True           # 也可以继续打印调试信息
    }
)

#知识图检索
# 重新初始化 graph_store 指向已存储的数据库
print("[DEBUG] Step 3: Initializing GraphRAGStore...")
gs_start_time = time.time()
graph_store = GraphRAGStore(
    username="neo4j", password="ZX20010103", url="bolt://192.168.9.116:7687",
        refresh_schema=False,     # ← 关闭自动刷新
        #create_indexes=False,     # ← 如无新增索引需求，可一并关闭
        # enhanced_schema=False     # ← 关闭深度 schema 分析
)
gs_end_time = time.time()
print(f"[DEBUG] Step 4: Initializing GraphRAGStore Finished in:{gs_end_time - gs_start_time}s")
# 初始化嵌入模型（这步一般不影响查询，只用于衡量相似度）
# embed_model = HuggingFaceEmbedding(
#     model_name=EMBEDDINGS_MODEL, device="cuda"
# )
# 此处不用传入节点和kg_extractor，因为知识图已经构建好
graph_index = PropertyGraphIndex(
    llm=llm,
    nodes=[],  # 或者不传入节点
    kg_extractors=[],  # 不需要抽取新信息
    embed_model=embeddings,
    property_graph_store=graph_store,
    show_progress=False,
)

graph_index.property_graph_store.build_communities()
# 3. 构建图检索引擎
query_engine = GraphRAGQueryEngine(
    graph_store=graph_index.property_graph_store,
    llm=llm,
    index=graph_index,
    similarity_top_k=10
)

def GV_hybrid_rag(sample: dict) -> str:
    """
    输入 sample 字典（含 question_text），
    返回模型生成的简短答案。
    """
    q = sample["question_text"]

    # 2.1 向量检索
    vec_ans   = qa_chain.run(q)
    # 2.2 图检索
    graph_ans = query_engine.get_all_community_answers(q)

    # 2.3 最终聚合 Prompt，强制“只输出答案”
    prompt = f"""
single-hop and multi-hop dataset QA evaluation is in progress!
Please refer to the answer format of the following QA examples and give a brief answer. Make sure to end with "the answer is":
[Examples]:
    Q: Nobody Loves You was written by John Lennon and released on what album that was issued by Apple Records, and was written, recorded, and released during his 18 month separation from Yoko Ono?
    A: The album issued by Apple Records, and written, recorded, and released during John Lennon's 18 month separation from Yoko Ono is Walls and Bridges. Nobody Loves You was written by John Lennon on Walls and Bridges album. So the answer is: Walls and Bridges.

    Q: What year did Edburga of Minster-in-Thanet's father die?
    A: The father of Edburga of Minster-in-Thanet is King Centwine. Centwine died after 685. So the answer is: after 685.
Please extract following contextual reasoning information from vector answers and graph answers that helps answer the query:
[USER QUERY]
Query: {q}

[Vector Answer]
{vec_ans}

[Graph Answers]
{json.dumps(graph_ans)}
Answer:"""
    final = llm.complete(prompt).text
    return final.strip()

def direct_llm_answer(question: str) -> str:
    """
    A类回答：不依赖检索，直接调用LLM对问题进行回答。
    输入：question字符串
    输出：LLM生成的简短答案字符串
    """
    # 构造简单Prompt
    prompt = f"""
    You are an expert in knowledge reasoning question answering. Please reasoning step by step to answer the question as the QA examples below.
    Remember refering to the answer format of the following QA examples and give a brief answer. Make sure to end with "the answer is":
    [Examples]
    Q:Jeremy Theobald and Christopher Nolan share what profession?
    A:Jeremy Theobald is an actor and producer. Christopher Nolan is a director, producer, and screenwriter. Therefore, they both share the profession of being a producer. So the answer is: producer.
    
    Q:What film directed by Brian Patrick Butler was inspired by a film directed by F.W. Murnau?
    A: Brian Patrick Butler directed the film The Phantom Hour. The Phantom Hour was inspired by the films such as Nosferatu and The Cabinet of Dr. Caligari. Of these Nosferatu was directed by F.W. Murnau. So the answer is: The Phantom Hour.
    
    Question: {question}
    Answer:
    """
    # 调用LLM生成
    response = llm.complete(prompt)
    # 提取并返回文本
    return response.text.strip()

# vector_retrieval.py

def vector_retrieval_answer(question: str) -> str:
    """
    B类回答：通过向量检索（FAISS + RetrievalQA）获取候选，并返回模型生成的答案。
    输入：question字符串
    输出：检索结果文本
    """
    # 直接执行向量检索链
    answer = qa_chain.run(question)
    return answer.strip()