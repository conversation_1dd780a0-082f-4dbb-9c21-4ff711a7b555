# evaluate_rag.py

import os
import json
import jsonlines
import re
from collections import Counter
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

from answergen_hybrid import GV_hybrid_rag  # 导入上面模块

# 配置
DATASETS = {
    "hotpotqa": "G:/PAPER/Experiments/MBA-main/processed_data/squad/test_subsampled.jsonl",
    # "squad":    "squad_test.jsonl",
    # "nq":       "nq_test.jsonl",
    # "musique":  "musique_test.jsonl"
}
OUTPUT_DIR = "predictions"
NUM_WORKERS = 5

# ─── 1. 数据加载 ───
def load_jsonl(path):
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            yield json.loads(line)

# ─── 2. 批量预测 ───
def batch_predict(dataset_name, samples):
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    out_file = os.path.join(OUTPUT_DIR, f"{dataset_name}.jsonl")

    # 跳过已做
    done = set()
    if os.path.exists(out_file):
        with open(out_file, "r", encoding="utf-8") as f:
            for line in f:
                done.add(json.loads(line)["question_id"])

    with jsonlines.open(out_file, mode="a") as writer, \
         ThreadPoolExecutor(max_workers=NUM_WORKERS) as exe:
        fut_to_qid = {
            exe.submit(answer_question, s): s["question_id"]
            for s in samples if s["question_id"] not in done
        }
        for fut in tqdm(as_completed(fut_to_qid), total=len(fut_to_qid),
                        desc=f"Predicting {dataset_name}"):
            qid = fut_to_qid[fut]
            try:
                pred = fut.result()
                writer.write({"question_id": qid, "prediction": pred})
            except Exception as e:
                print(f"[Error] {dataset_name} {qid}: {e}")

# ─── 3. 指标计算 ───
def normalize(text: str) -> str:
    text = text.lower()
    text = re.sub(r"[^\w\s]", "", text)
    return " ".join(text.split())

def compute_em(pred: str, gold: str) -> int:
    return int(normalize(pred) == normalize(gold))

def compute_f1(pred: str, gold: str) -> float:
    p_tokens = normalize(pred).split()
    g_tokens = normalize(gold).split()
    common = Counter(p_tokens) & Counter(g_tokens)
    same = sum(common.values())
    if same == 0: return 0.0
    prec  = same / len(p_tokens)
    recall= same / len(g_tokens)
    return 2*prec*recall/(prec+recall)

def compute_acc(pred: str, golds: list) -> int:
    np = normalize(pred)
    for g in golds:
        if normalize(g) in np:
            return 1
    return 0  # ACC 定义：预测包含任一金标准即正确 

def evaluate_dataset(dataset_name, samples):
    # 3.1 读预测
    pred_path = os.path.join(OUTPUT_DIR, f"{dataset_name}.jsonl")
    preds = {r["question_id"]: r["prediction"]
             for r in load_jsonl(pred_path)}

    # 3.2 遍历样本计算指标
    total_em, total_f1, total_acc, cnt = 0, 0.0, 0, 0
    for s in samples:
        qid   = s["question_id"]
        golds = [span for obj in s["answers_objects"] for span in obj["spans"]]
        if qid not in preds: continue
        p = preds[qid]
        em_scores  = [compute_em(p, g) for g in golds]
        f1_scores  = [compute_f1(p, g) for g in golds]
        total_em  += max(em_scores)
        total_f1 += max(f1_scores)
        total_acc+= compute_acc(p, golds)
        cnt       += 1

    print(f"{dataset_name} 样本数: {cnt}")
    print(f"{dataset_name} EM:  {total_em/cnt*100:.2f}%")
    print(f"{dataset_name} F1:  {total_f1/cnt*100:.2f}%")
    print(f"{dataset_name} ACC: {total_acc/cnt*100:.2f}%\n")

# ─── 4. 运行入口 ───
if __name__ == "__main__":
    for name, path in DATASETS.items():
        print(f"=== 评估 {name} ===")
        samples = list(load_jsonl(path))
        batch_predict(name, samples)
        evaluate_dataset(name, samples)
