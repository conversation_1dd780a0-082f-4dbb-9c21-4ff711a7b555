import os
import json
import jsonlines
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm
from threading import Lock
import time

# 导入各类回答函数
from answergen_choose_rag import GV_hybrid_rag, vector_retrieval_answer, direct_llm_answer

# 配置路径
DATASETS = {
    # "hotpotqa": "G:/PAPER/Experiments/MBA-main/processed_data/hotpotqa/test_subsampled.jsonl",
    # "nq": "G:/PAPER/Experiments/MBA-main/processed_data/nq/test_subsampled.jsonl",
    # "squad": "G:/PAPER/Experiments/MBA-main/processed_data/squad/test_subsampled.jsonl"
    "musique":"G:/PAPER/Experiments/MBA-main/processed_data/musique/test_subsampled.jsonl"
}
CLASSIFICATION_PATH = "G:/PAPER/Experiments/MBA-main/processed_data/dataset_splits/musique.jsonl"  # 分类结果 .jsonl 文件
OUTPUT_DIR = "predictions"
NUM_WORKERS = 5

# 全局策略计数
strategy_counts = {"A": 0, "B": 0, "C": 0}
lock = Lock()

# ─── 1. 数据加载 ───
def load_jsonl(path):
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            yield json.loads(line)

# ─── 2. 加载分类结果 ───
import jsonlines

def load_classification(path):
    classification_map = {}
    with jsonlines.open(path, mode='r') as reader:
        for obj in reader:
            qid = obj.get('id') or obj.get('question_id')
            if qid:
                classification_map[qid] = {
                    'prediction': obj.get('prediction', 'C'),
                    'question': obj.get('question', None)
                }
    return classification_map

# ─── 3. 单样本回答 ───
def answer_question(sample, cls_map):
    qid = sample.get("question_id")
    question = sample.get("question", sample.get("question_text", ""))
    # 从分类映射取值
    cls_entry = cls_map.get(qid, {})
    cls = cls_entry.get("prediction", "C")

    # 更新策略计数
    with lock:
        strategy_counts[cls] = strategy_counts.get(cls, 0) + 1
    print(f"[DEBUG] Question {qid}: using strategy {cls}")

    # 根据策略调用对应方法
    if cls == "A":
        question_to_use = cls_entry.get('question', question)
        return direct_llm_answer(question_to_use)
    elif cls == "B":
        question_to_use = cls_entry.get('question', question)
        return vector_retrieval_answer(question_to_use)
    else:
        return GV_hybrid_rag(sample)

# ─── 4. 批量预测 ───
def batch_predict(dataset_name, samples, cls_map):
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    out_file = os.path.join(OUTPUT_DIR, f"{dataset_name}.jsonl")

    # 跳过已完成
    done = set()
    if os.path.exists(out_file):
        with open(out_file, "r", encoding="utf-8") as f:
            for line in f:
                done.add(json.loads(line)["question_id"])

    with jsonlines.open(out_file, mode="a") as writer, \
         ThreadPoolExecutor(max_workers=NUM_WORKERS) as exe:
        fut2qid = {}
        for s in samples:
            qid = s.get("question_id")
            if qid in done:
                continue
            fut = exe.submit(answer_question, s, cls_map)
            fut2qid[fut] = qid

        for fut in tqdm(as_completed(fut2qid), total=len(fut2qid), desc=f"Predicting {dataset_name}"):
            qid = fut2qid[fut]
            try:
                pred = fut.result()
                writer.write({"question_id": qid, "prediction": pred})
            except Exception as e:
                print(f"[Error] {dataset_name} {qid}: {e}")

    # 打印本轮统计结果
    print(f"=== Strategy counts for {dataset_name} ===")
    for strat, cnt in strategy_counts.items():
        print(f"  Strategy {strat}: {cnt}")

# ─── 5. 主入口 ───
if __name__ == "__main__":
    # 加载分类结果
    classification_map = load_classification(CLASSIFICATION_PATH)
    total_time_start = time.time()
    for name, path in DATASETS.items():
        print(f"=== Predicting {name} ===")
        samples = list(load_jsonl(path))
        # 重置计数
        with lock:
            for k in strategy_counts:
                strategy_counts[k] = 0
        batch_predict(name, samples, classification_map)
    total_time_end = time.time()
    print(f"total time:{total_time_end-total_time_start}")
    
# 注意设置：该脚本的test_subsample.jsonl及dataset_split/{数据集}，以及！图摘要cache文件，指定评估数据集的vectorstore.faiss等，注意切换neo4j图服务器为要评估的数据集！！
#注！记得删除已有的{dataset_name}.jsonl文件！！！！
