from dataclasses import dataclass

@dataclass
class RAGConfig:
    # 向量检索配置
    vector_store_path: str = "D:/qxd/QA-HybridRAG-module/vector_index/nq_vectorstore"
    embedding_model: str = "D:/qxd/QA-HybridRAG-module/BAAI/bge-base-en-v1___5"
    vector_top_k: int = 5

    # 图检索配置
    graph_url: str = "bolt://localhost:7687"
    graph_username: str = "neo4j"
    graph_password: str = "password"
    graph_similarity_top_k: int = 10

    # LLM配置 - 硅基流动
    llm_vec_url: str = "https://api.siliconflow.cn/v1"
    llm_vec_model: str = "Qwen/Qwen3-7B-Instruct"
    llm_vec_api_key: str = "sk-azesajhqfqlqxtzxdfhbonkvvmbiwtkyzyttrmdzoujpvzen"
    llm_detail_url: str = "https://api.siliconflow.cn/v1"
    llm_detail_model: str = "Qwen/Qwen3-8B"
    llm_detail_api_key: str = "sk-cfwwbxkqjwhqvzdfxmugohhebslsopdhuhrixrsverdomsrg"

    # 原有LLM配置（已注释）
    # llm_vec_url: str = "http://***************:1025/v1"
    # llm_vec_model: str = "ds-r1-32b"
    # llm_detail_url: str = "http://***************:1025/v1"
    # llm_detail_model: str = "llama_65b"

    # 评估配置
    dataset_path: str = "musique_v1.0_dev.jsonl"
    metrics: list = ["accuracy", "recall", "f1"]
