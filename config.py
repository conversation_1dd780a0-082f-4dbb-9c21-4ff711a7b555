from dataclasses import dataclass

@dataclass
class RAGConfig:
    # 向量检索配置
    vector_store_path: str = "/home/<USER>/QA-HybridRAG/vector_index/nq_vectorstore/"
    embedding_model: str = "/home/<USER>/bge-base-en-v1.5/"
    vector_top_k: int = 5
    
    # 图检索配置
    graph_url: str = "bolt://192.168.224.43:7687"
    graph_username: str = "neo4j"
    graph_password: str = "ZX20010103"
    graph_similarity_top_k: int = 10
    
    # LLM配置
    llm_vec_url: str = "http://192.168.224.160:1025/v1"
    llm_vec_model: str = "ds-r1-32b"
    llm_detail_url: str = "http://192.168.224.160:1025/v1"
    llm_detail_model: str = "llama_65b"
    
    # 评估配置
    dataset_path: str = "musique_v1.0_dev.jsonl"
    metrics: list = ["accuracy", "recall", "f1"]
