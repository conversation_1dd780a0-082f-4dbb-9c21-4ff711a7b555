import re
import networkx as nx
from graspologic.partition import hierarchical_leiden
from collections import defaultdict
import os
import pickle
from llama_index.core.llms import ChatMessage
from .llamaindex_wrapper import CustomLLM,QwenLLM
from llama_index.graph_stores.neo4j import Neo4jPropertyGraphStore


class GraphRAGStore(Neo4jPropertyGraphStore):
    community_summary = {}
    entity_info = None
    max_cluster_size = 5
    communities_cache_file = "squad_communities_cache.pkl"
    
    def save_communities(self):
        """将 entity_info 和 community_summary 序列化保存到磁盘。"""
        data = {
            "entity_info": self.entity_info,
            "community_summary": self.community_summary
        }
        with open(self.communities_cache_file, "wb") as f:
            pickle.dump(data, f)
        print("Saved communities cache to disk.")

    def load_communities(self):
        print("Starting Loaded communities from cache.")
        """尝试从磁盘加载社区缓存数据。"""
        if os.path.exists(self.communities_cache_file):
            with open(self.communities_cache_file, "rb") as f:
                data = pickle.load(f)
                self.entity_info = data.get("entity_info")
                self.community_summary = data.get("community_summary")
            print("Loaded communities from cache Finised.")
            return True
        return False

    def generate_community_summary(self, text):
        """Generate summary for a given text using an LLM."""
        messages = [
            ChatMessage(
                role="system",
                content=(
                    "You are provided with a set of relationships from a knowledge graph, each represented as "
                    "entity1->entity2->relation->relationship_description. Your task is to create a summary of these "
                    "relationships. The summary should include the names of the entities involved and a concise synthesis "
                    "of the relationship descriptions. The goal is to capture the most critical and relevant details that "
                    "highlight the nature and significance of each relationship. Ensure that the summary is coherent and "
                    "integrates the information in a way that emphasizes the key aspects of the relationships."
                ),
            ),
            ChatMessage(role="user", content=text),
        ]
        response = QwenLLM(
    api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
    model="qwen-turbo-latest",
    max_tokens=2048,
    temperature=0.5,
    top_p=0.95,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    streaming=False                    
        ).chat(messages)
        # 假定 response 是 ChatResponse 对象，其 message 字段为 ChatMessage 对象
        clean_response = re.sub(r"^assistant:\s*", "", response.message.content).strip()
        return clean_response

    def build_communities(self):
        """Builds communities from the graph and summarizes them."""
        print("[STEP1] Attempting to load communities from cache...")
        if self.load_communities():
            print("[STEP1] Loaded from cache successfully")
            return
        print("Building communities from scratch...")
        nx_graph = self._create_nx_graph()
        community_hierarchical_clusters = hierarchical_leiden(
            nx_graph, max_cluster_size=self.max_cluster_size
        )
        self.entity_info, community_info = self._collect_community_info(
            nx_graph, community_hierarchical_clusters
        )
        self._summarize_communities(community_info)
        self.save_communities()

    def _create_nx_graph(self):
        """Converts internal graph representation to NetworkX graph."""
        nx_graph = nx.Graph()
        triplets = self.get_triplets()
        for entity1, relation, entity2 in triplets:
            nx_graph.add_node(entity1.name)
            nx_graph.add_node(entity2.name)
            nx_graph.add_edge(
                relation.source_id,
                relation.target_id,
                relationship=relation.label,
                description=relation.properties["relationship_description"],
            )
        return nx_graph

    def _collect_community_info(self, nx_graph, clusters):
        """
        Collect information for each node based on their community,
        allowing entities to belong to multiple clusters.
        """
        entity_info = defaultdict(set)
        community_info = defaultdict(list)

        for item in clusters:
            node = item.node
            cluster_id = item.cluster

            # Update entity_info
            entity_info[node].add(cluster_id)

            for neighbor in nx_graph.neighbors(node):
                edge_data = nx_graph.get_edge_data(node, neighbor)
                if edge_data:
                    detail = f"{node} -> {neighbor} -> {edge_data['relationship']} -> {edge_data['description']}"
                    community_info[cluster_id].append(detail)

        # Convert sets to lists for easier serialization if needed
        entity_info = {k: list(v) for k, v in entity_info.items()}

        return dict(entity_info), dict(community_info)

    def _summarize_communities(self, community_info):
        """Generate and store summaries for each community."""
        for community_id, details in community_info.items():
            details_text = (
                "\n".join(details) + "."
            )  # Ensure it ends with a period
            self.community_summary[
                community_id
            ] = self.generate_community_summary(details_text)

    def get_community_summaries(self):
        """Returns the community summaries, building them if not already done."""
        if not self.community_summary:
            self.build_communities()
        return self.community_summary
    
   
