from langchain_community.graphs import Neo4jGraph
import os
import pandas as pd
from langchain.prompts import ChatPromptTemplate,PromptTemplate
from typing import List
from langchain_community.graphs.graph_document import GraphDocument
from langchain_core.documents import Document

os.environ["NEO4J_URI"] = "bolt://***********:7687"
os.environ["NEO4J_USERNAME"] = "neo4j"
os.environ["NEO4J_PASSWORD"] = "ZX20010103"
CUSTOM_PROMPT_TEMPLATE = """
You are an expert at extracting knowledge graphs from text. Given the following text, extract entities as nodes and the relationships between them.
Each node should be a JSON object with the following properties:
  - id: a unique identifier (string)
  - label: a descriptive label (string)
  - description: a brief description (string)

Each relationship should be a JSON object with:
  - source: the id of the source node (string)
  - target: the id of the target node (string)
  - description: a description of the relationship (string)

Your output must be a valid JSON object with two keys: "nodes" and "relationships". Do not include any additional text.

Text: {input}
"""

graph = Neo4jGraph(refresh_schema=False)

#dataset
news = pd.read_csv(
    "/home/<USER>/Exp/graph-rag/news_articles.csv"
)
# news["tokens"] = [
#     num_tokens_from_string(f"{row['title']} {row['text']}")
#     for i, row in news.iterrows()
# ]
# news.head()

from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_wrapper import CustomLLM

llm = CustomLLM(
    model_name="ds-r1-32b",
    base_url="http://***************:1025/v1",
    temperature=0.5,
    max_tokens=2048,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    top_p=0.95,
    streaming=False,  # 根据需求设置流式输出
    openai_api_key=""   # 如无验证则留空
)

# 使用 PromptTemplate 包装你的提示
prompt_template = PromptTemplate(
    input_variables=["input"],
    template=CUSTOM_PROMPT_TEMPLATE
)

llm_transformer = LLMGraphTransformer(
  llm=llm, 
    # node_properties=["description"],
    # relationship_properties=["description"],
    prompt=prompt_template  # 传入自定义的 prompt 模板
)

def process_text(text: str) -> List[GraphDocument]:
    doc = Document(page_content=text)
    return llm_transformer.convert_to_graph_documents([doc])

from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

MAX_WORKERS = 10
NUM_ARTICLES = 2000
graph_documents = []

with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
    # Submitting all tasks and creating a list of future objects
    futures = [
        executor.submit(process_text, f"{row['title']} {row['text']}")
        for i, row in news.head(NUM_ARTICLES).iterrows()
    ]

    for future in tqdm(
        as_completed(futures), total=len(futures), desc="Processing documents"
    ):
        graph_document = future.result()
        graph_documents.extend(graph_document)

graph.add_graph_documents(
    graph_documents,
    baseEntityLabel=True,
    include_source=True
)