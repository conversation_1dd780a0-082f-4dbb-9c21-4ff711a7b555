import os
os.environ["OPENBLAS_NUM_THREADS"] = "4"
import pandas as pd
from llama_index.core import Document
from typing import Any
from GraphRAGExtractor import GraphRAGExtractor
from GraphRAGStore import GraphRAGStore
from llamaindex_wrapper import CustomLLM,QwenLLM
from GraphRAGQueryEngine import GraphRAGQueryEngine
import json
llm = QwenLLM(
api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
model="qwen-turbo-latest",
max_tokens=2048,
temperature=0.5,
top_p=0.95,
presence_penalty=1.03,
frequency_penalty=1.0,
streaming=False
)

def save_extraction_result(node_index: int, node_text: str, raw_response: str, parsed_entities: list, parsed_relationships: list) -> None:
    """保存每个节点的处理中间数据到本地文件"""
    result = {
        "node_index": node_index,
        "node_text": node_text,
        "raw_response": raw_response,
        "entities": parsed_entities,
        "relationships": parsed_relationships,
    }
    # 以 JSON Lines 格式追加写入文件
    with open(SAVE_FILE, "a", encoding="utf-8") as f:
        f.write(json.dumps(result, ensure_ascii=False) + "\n")

def load_squad_data(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            yield json.loads(line)
            
def build_documents(data):
    documents = []
    for sample in data:
        question = sample['question_text']
        contexts = sample.get('contexts', [])
        supporting_contexts = [ctx['paragraph_text'] for ctx in contexts if ctx.get('is_supporting')]
        context_text = "\n".join(supporting_contexts)
        full_text = f"Question: {question}\nContext:\n{context_text}"
        documents.append(Document(text=full_text))
    return documents



# news = pd.read_csv(
#     "G:/PAPER/Experiments/Paper_Exp/QA-HybridRAG/graph-rag/news_articles.csv"
# )[:10]

# print(news.head())

# documents = [
#     Document(text=f"{row['title']}: {row['text']}")
#     for i, row in news.iterrows()
# ]

from llama_index.core.node_parser import SentenceSplitter

dataset = load_squad_data('G:/PAPER/Experiments/MBA-main/processed_data/nq/test_subsampled.jsonl')
documents = build_documents(dataset)

splitter = SentenceSplitter(
    chunk_size=2048,
    chunk_overlap=10,
)

nodes = splitter.get_nodes_from_documents(documents)
print(len(nodes))

KG_TRIPLET_EXTRACT_TMPL = """
-Goal-
Given a text document, identify all entities and their entity types from the text and all relationships among the identified entities.
Given the text, extract up to {max_knowledge_triplets} entity-relation triplets.

-Steps-
1. Identify all entities. For each identified entity, extract the following information:
- entity_name: Name of the entity, capitalized
- entity_type: Type of the entity
- entity_description: Comprehensive description of the entity's attributes and activities

2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.
For each pair of related entities, extract the following information:
- source_entity: name of the source entity, as identified in step 1
- target_entity: name of the target entity, as identified in step 1
- relation: relationship between source_entity and target_entity
- relationship_description: explanation as to why you think the source entity and the target entity are related to each other

3. Output Formatting:
- Return the result in valid JSON format with two keys: 'entities' (list of entity objects) and 'relationships' (list of relationship objects).
- Exclude any text outside the JSON structure (e.g., no explanations or comments).
- If no entities or relationships are identified, return empty lists: { "entities": [], "relationships": [] }.

-An Output Example-
{
  "entities": [
    {
      "entity_name": "Albert Einstein",
      "entity_type": "Person",
      "entity_description": "Albert Einstein was a theoretical physicist who developed the theory of relativity and made significant contributions to physics."
    },
    {
      "entity_name": "Theory of Relativity",
      "entity_type": "Scientific Theory",
      "entity_description": "A scientific theory developed by Albert Einstein, describing the laws of physics in relation to observers in different frames of reference."
    },
    {
      "entity_name": "Nobel Prize in Physics",
      "entity_type": "Award",
      "entity_description": "A prestigious international award in the field of physics, awarded annually by the Royal Swedish Academy of Sciences."
    }
  ],
  "relationships": [
    {
      "source_entity": "Albert Einstein",
      "target_entity": "Theory of Relativity",
      "relation": "developed",
      "relationship_description": "Albert Einstein is the developer of the theory of relativity."
    },
    {
      "source_entity": "Albert Einstein",
      "target_entity": "Nobel Prize in Physics",
      "relation": "won",
      "relationship_description": "Albert Einstein won the Nobel Prize in Physics in 1921."
    }
  ]
}

-Real Data-
######################
text: {text}
######################
output:"""

import json
import re


def parse_fn(response_str: str) -> Any:
    print("Raw LLM Response:")
    print(response_str)
    json_pattern = r"\{.*\}"
    match = re.search(json_pattern, response_str, re.DOTALL)
    entities = []
    relationships = []
    if not match:
        return entities, relationships
    json_str = match.group(0)
    try:
        data = json.loads(json_str)
        entities = [
            (
                entity.get("entity_name", ""),
                entity.get("entity_type", ""),
                entity.get("entity_description", "")
            )
            for entity in data.get("entities", [])
        ]
        relationships = [
            (
                relation.get("source_entity", ""),
                relation.get("target_entity", ""),
                relation.get("relation", ""),
                relation.get("relationship_description", "")
            )
            for relation in data.get("relationships", [])
        ]
        print("\nParsed Entities:")
        for ent in entities:
            print(ent)
        print("\nParsed Relationships:")
        for rel in relationships:
            print(rel)
        return entities, relationships
    except json.JSONDecodeError as e:
        print("Error parsing JSON:", e)
        return entities, relationships


kg_extractor = GraphRAGExtractor(
    llm=llm,
    extract_prompt=KG_TRIPLET_EXTRACT_TMPL,
    max_paths_per_chunk=2,
    parse_fn=parse_fn,
)

from llama_index.graph_stores.neo4j import Neo4jPropertyGraphStore

# Note: used to be `Neo4jPGStore`
graph_store = GraphRAGStore(
    username="neo4j", password="ZX20010103", url="bolt://***********:7687"
)
from llama_index.core import PropertyGraphIndex
from llama_index.embeddings.huggingface import HuggingFaceEmbedding

embed_model = HuggingFaceEmbedding(             
                                   
    model_name="G:/PAPER/Experiments/Paper_Exp/bge-base-en-v1.5",device="cuda"  # 模型名称（与本地存储路径对应）
)

index = PropertyGraphIndex(
    nodes=nodes,
    kg_extractors=[kg_extractor],
    embed_model=embed_model,
    property_graph_store=graph_store,
    show_progress=True,
)
print(index.property_graph_store.get_triplets()[10])
print(index.property_graph_store.get_triplets()[10][0].properties)
print(index.property_graph_store.get_triplets()[10][1].properties)

# index.property_graph_store.build_communities()

# query_engine = GraphRAGQueryEngine(
#     graph_store=index.property_graph_store,
#     llm=llm,
#     index=index,
#     similarity_top_k=10,
# )

# response = query_engine.query(
#     "What are the main news discussed in the document?"
# )
# display(Markdown(f"{response.response}"))

# response = query_engine.query("What are the main news in energy sector?")
# print(response)
