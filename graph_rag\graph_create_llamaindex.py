
import pandas as pd
from llama_index.core import Document
from typing import Any
from GraphRAGExtractor import GraphRAGExtractor
from GraphRAGStore import GraphRAGStore
from llamaindex_wrapper import CustomLLM
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
import os
os.environ["OPENBLAS_NUM_THREADS"] = "4"  
llm = CustomLLM(
    base_url="http://192.168.224.160:1025/v1",
    model="llama_65b",
    max_tokens=1024,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    temperature=0.05,
    top_p=0.92,
    streaming=False,
)

news = pd.read_csv(
    "/home/<USER>/graph-rag/news_articles.csv"
)[:50]

print(news.head())

documents = [
    Document(text=f"{row['title']}: {row['text']}")
    for i, row in news.iterrows()
]

from llama_index.core.node_parser import SentenceSplitter

splitter = SentenceSplitter(
    chunk_size=1024,
    chunk_overlap=20,
)
nodes = splitter.get_nodes_from_documents(documents)
print(len(nodes))

# prompts.py
KG_TRIPLET_EXTRACT_TMPL = """
-Goal-
Extract up to {max_knowledge_triplets} entity-relation triplets from the text below.

For each entity, output an object with these keys:
  "entity_name": the entity's name (capitalized),
  "entity_type": the entity's type (e.g., "Person", "Organization"),
  "entity_description": a short description.

For each relationship, output an object with these keys:
  "source_entity": name of the source entity,
  "target_entity": name of the target entity,
  "relation": the relationship label,
  "relationship_description": a brief explanation.

-Output-
Return a valid JSON object with exactly two keys: "entities" and "relationships".
Only output the JSON (no extra text), and use only double quotes.
Note! The key value must be consistent with the key value in the example! Do not add extra symbols at both ends of the key value!

-Example-
{
  "entities": [
    {
      "entity_name": "Tesla",
      "entity_type": "Organization",
      "entity_description": "An American electric vehicle company."
    }
  ],
  "relationships": [
    {
      "source_entity": "Tesla",
      "target_entity": "SpaceX",
      "relation": "collaborates_with",
      "relationship_description": "Tesla collaborates with SpaceX on technology."
    }
  ]
}

-Text-
{text}
"""
import json
import re
def fix_json_format(raw_response: str) -> str:
    """
    将原始响应传递给 LLM，让其修正格式问题，
    要求返回严格有效的 JSON，不包含额外的文本。
    """
    # 构造一个修正格式的提示词，这里可以根据需要进行调整
    fix_prompt = (
        "The following text is intended to be a JSON object but has formatting errors. "
        "There must not be any other symbols."
        "Input text:\n\n" + raw_response
    )
    # 使用 LLM 调用修正 JSON 格式。这里假设使用 complete() 方法返回结果，
    # 你可能需要根据你的调用方式进行修改：
    fixed_response = llm.complete(fix_prompt)
    # 假设返回结果存储在 fixed_response.text 中
    
    return fixed_response.text

def parse_fn(response_str: str) -> (list, list):
    """
    尝试解析 LLM 响应的 JSON内容。如果解析错误，则调用 fix_json_format() 修正格式，
    然后重新解析。最终返回实体和关系的列表，同时打印最终提取的结果供调试查看。
    """
    print("Raw LLM Response:")
    print(response_str)
    
    # 提取最外层 JSON 部分，若找不到，则使用原始响应
    json_pattern = r"\{.*\}"
    match = re.search(json_pattern, response_str, re.DOTALL)
    entities = []
    relationships = []
    if not match:
        return entities, relationships
    json_str = match.group(0)
    try:
        data = json.loads(json_str)
        entities = [
            (
                entity["entity_name"],
                entity["entity_type"],
                entity["entity_description"],
            )
            for entity in data.get("entities", [])
        ]
        relationships = [
            (
                relation["source_entity"],
                relation["target_entity"],
                relation["relation"],
                relation["relationship_description"],
            )
            for relation in data.get("relationships", [])
        ]
        print("\nParsed Entities:")
        for ent in entities:
            print(ent)
        print("\nParsed Relationships:")
        for rel in relationships:
            print(rel)

        return entities, relationships   
    # 打印最终提取的实体和关系
      
    except json.JSONDecodeError as e:
        print("Error parsing JSON:", e)
        return entities, relationships
       
        

kg_extractor = GraphRAGExtractor(
    llm=llm,
    extract_prompt=KG_TRIPLET_EXTRACT_TMPL,
    max_paths_per_chunk=2,
    parse_fn=parse_fn,
)

from llama_index.graph_stores.neo4j import Neo4jPropertyGraphStore

# Note: used to be `Neo4jPGStore`
graph_store = GraphRAGStore(
    username="neo4j", password="ZX20010103", url="bolt://**************:7687"
)
from llama_index.core import PropertyGraphIndex

EMBEDDINGS_MODEL="/home/<USER>/bge-base-en-v1.5/"

embed_model = HuggingFaceEmbedding(
model_name=EMBEDDINGS_MODEL,
)

index = PropertyGraphIndex(
    nodes=nodes,
    kg_extractors=[kg_extractor],
    property_graph_store=graph_store,
    embed_model=embed_model,
    show_progress=True,
)
print(index.property_graph_store.get_triplets()[10])
print(index.property_graph_store.get_triplets()[10][0].properties)
print(index.property_graph_store.get_triplets()[10][1].properties)

index.property_graph_store.build_communities()

query_engine = GraphRAGQueryEngine(
    graph_store=index.property_graph_store,
    llm=llm,
    index=index,
    similarity_top_k=10,
)

response = query_engine.query(
    "What are the main news discussed in the document?"
)
display(Markdown(f"{response.response}"))

response = query_engine.query("What are the main news in energy sector?")
print(response)
