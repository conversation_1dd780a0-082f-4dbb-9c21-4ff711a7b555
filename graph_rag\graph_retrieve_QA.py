from llama_index.graph_stores.neo4j import Neo4jPropertyGraphStore
from llama_index.core import PropertyGraphIndex
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from GraphRAGQueryEngine import GraphRAGQueryEngine 
from GraphRAGStore import GraphRAGStore
from llamaindex_wrapper import CustomLL<PERSON>,QwenLLM
from langchain_wrapper import Custom<PERSON>wenLL<PERSON>
print("[DEBUG] Step 2: Initializing QwenLLM...")
llm = QwenLLM(
    api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
    model="qwen-turbo-latest",
    max_tokens=2048,
    temperature=0.5,
    top_p=0.95,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    streaming=False
)
print("[DEBUG] Step 3: Initializing GraphRAGStore...")
graph_store = GraphRAGStore(
    username="neo4j", password="ZX20010103", url="bolt://10.1.40.104:7687"
)
print("[DEBUG] Step 4: Loading HuggingFaceEmbedding...")
embed_model = HuggingFaceEmbedding(
    model_name="G:/PAPER/Experiments/Paper_Exp/bge-base-en-v1.5", device="cuda"
)
print("[DEBUG] Step 5: Building PropertyGraphIndex...")
index = PropertyGraphIndex(
    llm=llm,
    nodes=[], 
    kg_extractors=[],  
    embed_model=embed_model,
    property_graph_store=graph_store,
    show_progress=False,
)
print("[DEBUG] Step 6: Building communities...")
#print(index.property_graph_store.get_triplets()[10])
#print(index.property_graph_store.get_triplets()[10][0].properties)
#print(index.property_graph_store.get_triplets()[10][1].properties)

index.property_graph_store.build_communities()

query_engine = GraphRAGQueryEngine(
    graph_store=index.property_graph_store,
    llm=llm,    
    index=index,
    similarity_top_k=10,
)


#response = query_engine.query("What are the main news discussed in the document?")
#print(response.response)

response = query_engine.query("who is david 's dad in the bible")
print(response.response)
