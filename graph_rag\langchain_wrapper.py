import requests
from typing import Optional, List, Mapping, Any
from langchain.llms.base import BaseLLM
from langchain_core.outputs.llm_result import LLMResult,Generation
from pydantic import Field 
from openai import OpenAI


class CustomLLM_LangChain(BaseLLM):
    # 将所有参数作为类字段提前声明
    model_name: str = Field(default="ds-32b")
    base_url: str = Field(default="http://192.168.226.166:9997/v1")
    temperature: float = Field(default=0.5)
    max_tokens: int = Field(default=20)
    presence_penalty: float = Field(default=1.03)
    frequency_penalty: float = Field(default=1.0)
    top_p: float = Field(default=0.95)
    seed: Optional[int] = Field(default=None)
    streaming: bool = Field(default=True)
    openai_api_key: str = Field(default="")
        # 添加一个属性用于标识不支持函数调用
    supports_function_call: bool = False
    
    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {
            "model_name": self.model_name,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "top_p": self.top_p,
            "seed": self.seed,
            "streaming": self.streaming,
        }
    
    @property
    def _llm_type(self) -> str:
        return "custom"

    def _call(self, prompt: str, stop: Optional[List[str]] = None) -> str:
        """
        调用在线 LLM 服务，返回生成文本。
        """
        url = f"{self.base_url}/chat/completions"
        data = {
            "model": self.model_name,
            "messages": [{"role": "system", "content": prompt}],
            "max_tokens": self.max_tokens,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "seed": self.seed,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "stream": self.streaming
        }
        headers = {
            "Accept": "application/json",
            "Content-type": "application/json"
        }
        if self.openai_api_key:
            headers["Authorization"] = f"Bearer {self.openai_api_key}"
            
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        json_response = response.json()
        # 假设返回格式为 {"choices": [{"message": {"content": "回复内容"}}]}
        return json_response.get("choices", [{}])[0].get("message", {}).get("content", "")
    
    def _generate(self, prompts: List[str], stop: Optional[List[str]] = None) -> LLMResult:
        """
        实现 _generate 方法，将多个 prompt 转换为 LLMResult 对象。
        对于每个 prompt，调用 _call 方法获取回复，然后封装为 Generation 对象。
        """
        generations = []
        for prompt in prompts:
            text = self._call(prompt, stop=stop)
            gen = Generation(text=text)
            generations.append([gen])
        return LLMResult(generations=generations)

class CustomQwenLLM(BaseLLM):
    """LangChain-compatible wrapper for Alibaba Cloud Tongyi Qianwen (OpenAI-compatible)."""

    model_name: str = Field(default="qwen-turbo-latest")
    base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1"
        # default="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    )
    temperature: float = Field(default=0.5)
    max_tokens: int = Field(default=512)
    top_p: float = Field(default=0.95)
    presence_penalty: float = Field(default=0.0)
    frequency_penalty: float = Field(default=0.0)
    stream: bool = Field(default=False)
    openai_api_key: str = Field(default="")

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {
            "model_name": self.model_name,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "stream": self.stream,
        }

    @property
    def _llm_type(self) -> str:
        return "custom_qwen"

    def _call(
        self, prompt: str, stop: Optional[List[str]] = None
    ) -> str:
        """同步调用 OpenAI 兼容 Chat Completions API，返回生成文本。"""
        url = f"{self.base_url}/chat/completions"  # 完整端点 :contentReference[oaicite:4]{index=4}
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            **({"Authorization": f"Bearer {self.openai_api_key}"} if self.openai_api_key else {}),
        }
        payload = {
            "model": self.model_name,
            "messages": [
                {"role": "system",  "content": prompt}
            ],
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "stream": self.stream,
        }
        resp = requests.post(url, json=payload, headers=headers)
        resp.raise_for_status()
        data = resp.json()
        # 提取第一个 choice 的 content :contentReference[oaicite:5]{index=5}
        return data["choices"][0]["message"]["content"]

    def _generate(
        self, prompts: List[str], stop: Optional[List[str]] = None
    ) -> LLMResult:
        """批量调用，返回 LLMResult 对象。"""
        all_generations = []
        for prompt in prompts:
            text = self._call(prompt, stop=stop)
            gen = Generation(text=text)
            all_generations.append([gen])
        return LLMResult(generations=all_generations)
    
    class CustomLlama2LLM(BaseLLM):
        """LangChain-compatible wrapper for Alibaba Cloud DashScope Llama2"""

    model_name: str = Field(default="llama3.3-70b-instruct")
    base_url: str = Field(
        default="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    )
    temperature: float = Field(default=0.5)
    max_tokens: int = Field(default=512)
    top_p: float = Field(default=0.95)
    stream: bool = Field(default=False)
    openai_api_key: str = Field(default="")

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {
            "model_name": self.model_name,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "stream": self.stream,
        }

    @property
    def _llm_type(self) -> str:
        return "custom_llama2"

    def _call(self, prompt: str, stop: Optional[List[str]] = None) -> str:
        payload = {
            "model": self.model_name,
            "input": {"messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]},
            "parameters": {"result_format": "message"}
        }
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        response = requests.post(self.base_url, json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data["output"]["choices"][0]["message"]["content"]

    def _generate(self, prompts: List[str], stop: Optional[List[str]] = None) -> LLMResult:
        generations = []
        for p in prompts:
            text = self._call(p, stop)
            generations.append([Generation(text=text)])
        return LLMResult(generations=generations)
    
