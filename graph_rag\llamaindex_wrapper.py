import requests
from typing import Any, Dict, Sequence, Optional, AsyncGenerator, Generator
from pydantic import Field,BaseModel
from llama_index.core.llms import ChatMessage, ChatResponse, CompletionResponse ,LLM
from llama_index.core.prompts import PromptTemplate
import logging
import asyncio

class CustomLLM(LLM):
    # 使用 pydantic 字段声明所有属性，注意将 "stream" 改为 "streaming"
    base_url: str
    model: str
    max_tokens: int
    presence_penalty: float
    frequency_penalty: float
    temperature: float
    top_p: float
    streaming: bool
    api_key: Optional[str] = None  # 新增API密钥字段
    system_prompt: Optional[str] = None
    headers: Dict[str, str] = Field(default_factory=lambda: {
        "Accept": "application/json",
        "Content-type": "application/json"
    })

    @property
    def metadata(self) -> Dict[str, Any]:
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "streaming": self.streaming,
            "base_url": self.base_url,
        }
    
    @classmethod
    def class_name(cls) -> str:
        return "CustomLLM"
    
    def chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponse:
        """
        同步 chat 接口：
        - 将 ChatMessage 列表转换为 JSON 请求 payload 并发送到在线服务。
        - 如果设置了 system_prompt，则在消息列表前添加系统消息。
        - 返回的 ChatResponse 对象，其 message 字段为一个 ChatMessage 实例。
        """
        payload_messages = [
        {"role": msg.role.value if hasattr(msg.role, "value") else str(msg.role), "content": msg.content}
        for msg in messages
        ]
        if self.system_prompt:
            # 同样确保 system_prompt 的 role 固定为 "system"
            payload_messages.insert(0, {"role": "system", "content": self.system_prompt})

        payload = {
            "model": self.model,
            "messages": payload_messages,
            "max_tokens": self.max_tokens,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "stream": self.streaming
        }
        print("Payload:", payload)  # 打印 payload 以便调试

        # 准备请求头，如果有API密钥则添加Authorization头
        headers = self.headers.copy()
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        url = f"{self.base_url}/chat/completions"
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
        return ChatResponse(message=ChatMessage(role="assistant", content=content))
    def complete(self, prompt: str, formatted: bool = False, **kwargs: Any) -> CompletionResponse:
        """
        同步 complete 接口：
        如果传入了额外参数且 formatted 为 False，则先格式化 prompt，
        如果设置了 system_prompt，则合并 system_prompt 与格式化后的 prompt，
        调用 chat 接口返回结果，再构造 CompletionResponse 对象。
        """
        # 如果 prompt 未格式化，但存在额外参数，进行格式化
        if not formatted and kwargs:
            try:
                prompt = prompt.format(**kwargs)
            except Exception as e:
                 print("Prompt 格式化错误:", e)
    
        # 合并 system_prompt 和 prompt，使得只发送一条消息（符合在线服务示例）
        if self.system_prompt:
            combined = f"{self.system_prompt}\n{prompt}"
            messages = [ChatMessage(role="system", content=combined)]
        else:
            messages = [ChatMessage(role="user", content=prompt)]  
        chat_resp = self.chat(messages)
        return CompletionResponse(text=chat_resp.message.content)
        
    
    def stream_chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> Generator[ChatResponse, None, None]:
        """同步流式 chat 接口：简单实现为一次性返回结果。"""
        yield self.chat(messages, **kwargs)
    
    def stream_complete(self, prompt: str, formatted: bool = False, **kwargs: Any) -> Generator[CompletionResponse, None, None]:
        """同步流式 complete 接口：简单实现为一次性返回结果。"""
        yield self.complete(prompt, formatted, **kwargs)
    
    async def achat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponse:
        """异步 chat 接口：包装同步 chat 方法。"""
        return await asyncio.to_thread(self.chat, messages, **kwargs)
    
    async def acomplete(self, prompt: str, formatted: bool = False, **kwargs: Any) -> CompletionResponse:
        """异步 complete 接口：包装同步 complete 方法。"""
        return await asyncio.to_thread(self.complete, prompt, formatted, **kwargs)
    
    async def astream_chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> AsyncGenerator[ChatResponse, None]:
        """异步流式 chat 接口：包装同步 stream_chat 方法。"""
        for resp in self.stream_chat(messages, **kwargs):
            yield resp
    
    async def astream_complete(self, prompt: str, formatted: bool = False, **kwargs: Any) -> AsyncGenerator[CompletionResponse, None]:
        """异步流式 complete 接口：包装同步 stream_complete 方法。"""
        for resp in self.stream_complete(prompt, formatted, **kwargs):
            yield resp

    async def apredict(self, prompt: str, **kwargs: Any) -> str:
        """
        异步预测接口：接收一个文本提示，
        利用 acomplete 方法获取 CompletionResponse，然后返回最终文本。
        """
        comp_response = await self.acomplete(prompt, formatted=False, **kwargs)
        return comp_response.text


class QwenLLM(LLM, BaseModel):
    base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1"
        # default="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    )
    api_key: Optional[str]
    model: str
    max_tokens: int
    presence_penalty: float
    frequency_penalty: float
    temperature: float
    top_p: float
    streaming: bool
    system_prompt: Optional[str] = None
    headers: Dict[str, str] = Field(default_factory=lambda: {
        "Accept": "application/json",
        "Content-type": "application/json"
    })

    @property
    def metadata(self) -> Dict[str, Any]:
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "streaming": self.streaming,
            "base_url": self.base_url,
        }

    @classmethod
    def class_name(cls) -> str:
        return "QwenLLM"

    def chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponse:
        # 构造 payload_messages，内容空字符串兜底
        payload_messages = []
        for msg in messages:
            content = msg.content if msg.content is not None else ""
            # 支持从 _additional_kwargs 拿 query_str/text
            extras = getattr(msg, "_additional_kwargs", {}) or {}
            if not content:
                content = extras.get("query_str") or extras.get("text") or ""
            # 占位符格式化
            try:
                content = content.format(**kwargs)
            except Exception:
                pass
            content = content.strip()
            role = getattr(msg.role, "value", str(msg.role))
            payload_messages.append({"role": role, "content": content})

        if self.system_prompt:
            payload_messages.insert(
                0,
                {"role": "system", "content": self.system_prompt.strip()}
            )

        payload = {
            "model": self.model,
            "messages": payload_messages,
            "max_tokens": self.max_tokens,
            "presence_penalty": self.presence_penalty,
            "frequency_penalty": self.frequency_penalty,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "stream": self.streaming
        }
        # 添加授权
        headers = self.headers.copy()
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        print("Payload:", payload)
        response = requests.post(f"{self.base_url}/chat/completions", json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        choices = data.get("choices", [])
        if not choices:
            raise ValueError("API response has no choices")
        content = choices[0].get("message", {}).get("content", "") or ""
        return ChatResponse(message=ChatMessage(role="assistant", content=content))

    def complete(
        self,
        prompt: str | PromptTemplate,
        formatted: bool = False,
        **kwargs: Any
    ) -> CompletionResponse:
        # prompt 格式化
        if isinstance(prompt, PromptTemplate):
            prompt_str = prompt.format(**kwargs)
        else:
            prompt_str = prompt.strip()
        # 合并 system_prompt
        if self.system_prompt:
            combined = f"{self.system_prompt.strip()}\n{prompt_str}"
            messages = [ChatMessage(role="system", content=combined)]
        else:
            messages = [ChatMessage(role="user", content=prompt_str)]
        chat_resp = self.chat(messages, **kwargs)
        return CompletionResponse(text=chat_resp.message.content)

    def stream_chat(
        self, messages: Sequence[ChatMessage], **kwargs: Any
    ) -> Generator[ChatResponse, None, None]:
        yield self.chat(messages, **kwargs)

    def stream_complete(
        self,
        prompt: str,
        formatted: bool = False,
        **kwargs: Any
    ) -> Generator[CompletionResponse, None, None]:
        yield self.complete(prompt, formatted, **kwargs)

    async def achat(
        self, messages: Sequence[ChatMessage], **kwargs: Any
    ) -> ChatResponse:
        return await asyncio.to_thread(self.chat, messages, **kwargs)

    async def acomplete(
        self,
        prompt: str,
        formatted: bool = False,
        **kwargs: Any
    ) -> CompletionResponse:
        return await asyncio.to_thread(self.complete, prompt, formatted, **kwargs)

    async def astream_chat(
        self, messages: Sequence[ChatMessage], **kwargs: Any
    ) -> AsyncGenerator[ChatResponse, None]:
        for resp in self.stream_chat(messages, **kwargs):
            yield resp

    async def astream_complete(
        self,
        prompt: str,
        formatted: bool = False,
        **kwargs: Any
    ) -> AsyncGenerator[CompletionResponse, None]:
        for resp in self.stream_complete(prompt, formatted, **kwargs):
            yield resp

    async def apredict(self, prompt: str, **kwargs: Any) -> str:
        comp = await self.acomplete(prompt, formatted=False, **kwargs)
        return comp.text
class Llama2LLM(LLM, BaseModel):
    base_url: str = Field(
        default="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    )
    api_key: str
    model: str = Field(default="llama2-7b-chat")
    max_tokens: int = Field(default=512)
    temperature: float = Field(default=0.5)
    top_p: float = Field(default=0.95)
    streaming: bool = Field(default=False)
    system_prompt: Optional[str] = None
    headers: Dict[str, str] = Field(default_factory=lambda: {
        "Accept": "application/json",
        "Content-Type": "application/json"
    })

    @property
    def metadata(self) -> Dict[str, Any]:
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "streaming": self.streaming,
            "base_url": self.base_url,
        }

    @classmethod
    def class_name(cls) -> str:
        return "Llama2LLM"

    def chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> ChatResponse:
        payload_messages = []
        for msg in messages:
            content = msg.content or ""
            payload_messages.append({"role": msg.role.value, "content": content})
        if self.system_prompt:
            payload_messages.insert(0, {"role": "system", "content": self.system_prompt})

        payload = {
            "model": self.model,
            "input": {"messages": payload_messages},
            "parameters": {"result_format": "message"}
        }
        headers = self.headers.copy()
        headers["Authorization"] = f"Bearer {self.api_key}"
        response = requests.post(self.base_url, json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        content = data["output"]["choices"][0]["message"]["content"]
        return ChatResponse(message=ChatMessage(role="assistant", content=content))

    def complete(self, prompt: str, formatted: bool = False, **kwargs: Any) -> CompletionResponse:
        msg = ChatMessage(role="user", content=prompt)
        chat_resp = self.chat([msg], **kwargs)
        return CompletionResponse(text=chat_resp.message.content)

    async def apredict(self, prompt: str, **kwargs: Any) -> str:
        return self.complete(prompt, **kwargs).text

    # 同步流接口简化实现
    def stream_chat(self, messages: Sequence[ChatMessage], **kwargs: Any) -> AsyncGenerator[ChatResponse, None]:
        yield self.chat(messages, **kwargs)

    def stream_complete(self, prompt: str, formatted: bool = False, **kwargs: Any) -> AsyncGenerator[CompletionResponse, None]:
        yield self.complete(prompt, formatted, **kwargs)
# -------------------------------
# 使用示例
# -------------------------------
if __name__ == "__main__":
    import asyncio
    from llama_index.core.llms import ChatMessage
    # 使用 pydantic 自动生成的 __init__，传入所有字段
    llm = CustomLLM(
            base_url="http://localhost:8421",
        model="flan-t5-xl",
        max_tokens=2048,
        presence_penalty=1.03,
        frequency_penalty=1.0,
        
        temperature=0.5,
        top_p=0.95,
        streaming=False,
    )
#     llm = QwenLLM(
#     api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
#     model="qwen-turbo-latest",
#     max_tokens=2048,
#     temperature=0.5,
#     top_p=0.95,
#     presence_penalty=1.03,
#     frequency_penalty=1.0,
#     streaming=False
# )

    # 测试 chat 接口
    response = llm.chat([ChatMessage(role="user", content="Hello")])
    print("Chat Response:", response.message.content)
    
    # 测试 complete 接口
    prompt_graph='\n-Goal-\nGiven a text document, identify all entities and their entity types from the text and all relationships among the identified entities.\nGiven the text, extract up to 2 entity-relation triplets.\n\n-Steps-\n1. Identify all entities. For each identified entity, extract the following information:\n- entity_name: Name of the entity, capitalized\n- entity_type: Type of the entity\n- entity_description: Comprehensive description of the entity\'s attributes and activities\n\n2. From the entities identified in step 1, identify all pairs of (source_entity, target_entity) that are *clearly related* to each other.\nFor each pair of related entities, extract the following information:\n- source_entity: name of the source entity, as identified in step 1\n- target_entity: name of the target entity, as identified in step 1\n- relation: relationship between source_entity and target_entity\n- relationship_description: explanation as to why you think the source entity and the target entity are related to each other\n\n3. Output Formatting:\n- Return the result in valid JSON format with two keys: \'entities\' (list of entity objects) and \'relationships\' (list of relationship objects).\n- Exclude any text outside the JSON structure (e.g., no explanations or comments).\n- If no entities or relationships are identified, return empty lists: { "entities": [], "relationships": [] }.\n\n-An Output Example-\n{\n  "entities": [\n    {\n      "entity_name": "Albert Einstein",\n      "entity_type": "Person",\n      "entity_description": "Albert Einstein was a theoretical physicist who developed the theory of relativity and made significant contributions to physics."\n    },\n    {\n'
    comp_response = llm.complete(prompt_graph)
    print("Completion Response:", comp_response.text)
    async def test_apredict():
        result = await llm.apredict("please introduce yourself")
        print("apredict 返回结果：", result)
    asyncio.run(test_apredict())

