import os
from langchain.schema import LLMResult
# 假设上面类定义在 custom_qwen.py 中
from langchain_wrapper import CustomQwenLLM,CustomLLM_LangChain

# 1. 设置密钥
os.environ["DASHSCOPE_API_KEY"] = "sk-efcf7126cc904ccaa7bd6a50e51192fe"

# 2. 初始化模型
# llm = CustomQwenLLM(
#     model_name="qwen-plus",
#     openai_api_key=os.getenv("DASHSCOPE_API_KEY"),
#     max_tokens=256,
#     temperature=0.3,
#     stream=False
# )
llm = CustomLLM_LangChain(        
        model_name="flan-t5-xl",
        base_url="http://localhost:8421",  # 不要加 /v1 前缀，类内部会拼接
        temperature=0.7,
        max_tokens=2048,
        stream=False,
    )

# 3. 单次预测
response_text = llm.predict("Hello, who are you?")  # .predict 调用 _call
print("Assistant:", response_text)

# 4. 批量生成示例
prompts = ["What is <PERSON><PERSON>wen?", "What does 'Hello world' mean to a programmer?."]
result: LLMResult = llm.generate(prompts)
for idx, gen_list in enumerate(result.generations):
    print(f"Prompt {idx} → {gen_list[0].text}")