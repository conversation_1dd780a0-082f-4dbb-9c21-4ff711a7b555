import json
import re
import string

def normalize_answer(s):
    """Lower text and remove punctuation, articles and extra whitespace."""

    def remove_articles(text):
        regex = re.compile(r"\b(a|an|the)\b", re.UNICODE)
        return re.sub(regex, " ", text)

    def white_space_fix(text):
        return " ".join(text.split())

    def remove_punc(text):
        exclude = set(string.punctuation)
        return "".join(ch for ch in text if ch not in exclude)

    def lower(text):
        return text.lower()

    return white_space_fix(remove_articles(remove_punc(lower(s))))

def answer_extractor(potentially_cot: str) -> str:
    cot_regex = re.compile(r".* answer is:? (.*)")
    match = cot_regex.match(potentially_cot)
    if match:
        output = match.group(1).strip()
        if output.endswith("."):
            output = output[:-1]
        return output
    return potentially_cot

# 假设预测结果存储在一个 JSON 文件中，每行一个 JSON 对象
predictions_file = "hotpotqa.jsonl"  # 替换为实际的预测结果文件路径
extracted_predictions = {}

with open(predictions_file, "r", encoding="utf-8") as f:
    for line in f:
        data = json.loads(line)
        question_id = data["question_id"]
        prediction = data["prediction"]
        extracted_answer = answer_extractor(prediction)
        normalized_answer = normalize_answer(extracted_answer)
        extracted_predictions[question_id] = normalized_answer

# 保存提取并标准化后的预测结果
output_file = "extracted_hotpot_predictions.json"  # 替换为实际的输出文件路径
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(extracted_predictions, f, indent=4)

print(f"提取并标准化后的预测结果已保存到 {output_file}")
    