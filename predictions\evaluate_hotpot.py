import os
import json
import jsonlines
import re
import string
from collections import Counter
import argparse


def answer_extractor(raw: str) -> str:
    """
    从原始 RAG 预测字符串中提取短答案。
    例如: "... So the answer is: American." -> "american"
    如果未匹配 "answer is" 模式，则返回首行小写内容。
    """
    raw = raw.strip()
    # 如果有引号包裹，去掉
    if raw.startswith('"') and raw.endswith('"'):
        raw = raw[1:-1]
    # 匹配 "answer is:" 之后最短匹配到句号前内容
    m = re.search(r"answer is[:]?\s*(.*?)(?:\.|$)", raw, re.IGNORECASE)
    if m:
        ans = m.group(1).strip()
    else:
        # 否则取第一行全部内容
        ans = raw.splitlines()[0].strip()
    return ans.lower()


def normalize_answer(s: str) -> str:
    """
    官方 HotpotQA normalize_answer 同步:
    - 小写
    - 去除标点
    - 去除冠词 a/an/the
    - 规范化空格
    """
    def remove_articles(text):
        return re.sub(r"\b(a|an|the)\b", " ", text)
    def remove_punc(text):
        # 使用 string.punctuation 避免语法错误
        return ''.join(ch for ch in text if ch not in set(string.punctuation))
    def white_space_fix(text):
        return ' '.join(text.split())
    text = s.lower()
    text = remove_punc(text)
    text = remove_articles(text)
    return white_space_fix(text)


def compute_em(pred: str, gold: str) -> int:
    """Exact Match: 规范化后完全一致则为1"""
    return int(normalize_answer(pred) == normalize_answer(gold))


def compute_f1(pred: str, gold: str) -> float:
    """Token 级别 F1"""
    p_tokens = normalize_answer(pred).split()
    g_tokens = normalize_answer(gold).split()
    common = Counter(p_tokens) & Counter(g_tokens)
    same = sum(common.values())
    if same == 0:
        return 0.0
    precision = same / len(p_tokens)
    recall = same / len(g_tokens)
    return 2 * precision * recall / (precision + recall)


def compute_acc(pred: str, golds: list) -> int:
    """Accuracy: 预测包含任一标准答案子串即为正确"""
    npred = normalize_answer(pred)
    for g in golds:
        if normalize_answer(g) in npred:
            return 1
    return 0


def load_predictions(pred_file: str) -> dict:
    """
    读取 JSONL 格式的 RAG 原始预测: 每行 {"question_id": ..., "prediction": ...}
    返回映射 question_id -> raw_prediction
    """
    preds = {}
    with open(pred_file, 'r', encoding='utf-8') as f:
        for line in f:
            rec = json.loads(line)
            preds[rec['question_id']] = rec['prediction']
    return preds


def extract_answers(preds_raw: dict) -> dict:
    """
    对原始预测调用 answer_extractor 并返回 question_id -> extracted_answer
    """
    return {qid: answer_extractor(raw) for qid, raw in preds_raw.items()}


def load_ground_truths(gt_file: str) -> dict:
    """
    从 JSONL 测试集加载 ground truth spans: 返回 question_id -> List[str]
    假设每行包含 'answers_objects' 列表
    """
    gts = {}
    with open(gt_file, 'r', encoding='utf-8') as f:
        for line in f:
            rec = json.loads(line)
            qid = rec['question_id']
            spans = []
            for obj in rec.get('answers_objects', []):
                spans.extend(obj.get('spans', []))
            gts[qid] = spans
    return gts


def evaluate(preds: dict, gts: dict) -> tuple:
    """
    计算 EM/F1/ACC 并返回平均值
    """
    total_em, total_f1, total_acc, count = 0, 0.0, 0, 0
    for qid, golds in gts.items():
        if qid not in preds:
            continue
        p = preds[qid]
        em_scores = [compute_em(p, g) for g in golds]
        f1_scores = [compute_f1(p, g) for g in golds]
        total_em += max(em_scores)
        total_f1 += max(f1_scores)
        total_acc += compute_acc(p, golds)
        count += 1
    return total_em/count if count else 0, total_f1/count if count else 0, total_acc/count if count else 0


def main():
    parser = argparse.ArgumentParser(
        description='从 RAG 原始预测中提取短答案并评估 EM/F1/ACC'
    )
    parser.add_argument('--pred_file', type=str,
                        default='squad.jsonl',
                        help='RAG 原始预测 JSONL 路径')
    parser.add_argument('--gt_file', type=str,
                        default='D:/work/llm_exp/processed_data/processed_data/squad/test_subsampled.jsonl',
                        help='测试集 JSONL 路径')
    parser.add_argument('--extracted_file', type=str,
                        default='./flan-t5-xl_3b_squad_ablation_allA.json',
                        help='保存提取后答案的 JSON 路径')
    parser.add_argument('--metrics_file', type=str,
                        default='./flan-t5-xl_3b_squad_ablation_allA_result.json',
                        help='保存评估指标的 JSON 路径（可选）')
    args = parser.parse_args()

    # 1. 读取并提取
    preds_raw = load_predictions(args.pred_file)
    preds_ext = extract_answers(preds_raw)

    # 2. 保存提取结果
    with open(args.extracted_file, 'w', encoding='utf-8') as f:
        json.dump(preds_ext, f, indent=2, ensure_ascii=False)
    print(f"已保存提取答案至 {args.extracted_file}")

    # 3. 加载 Ground Truth
    gts = load_ground_truths(args.gt_file)

    # 4. 评估
    em, f1, acc = evaluate(preds_ext, gts)
    print("Evaluation Results:")
    print(f"  EM:  {em*100:.2f}%")
    print(f"  F1:  {f1*100:.2f}%")
    print(f"  ACC: {acc*100:.2f}%")

    # 5. 可选保存指标
    if args.metrics_file:
        metrics = {'EM': em, 'F1': f1, 'ACC': acc}
        with open(args.metrics_file, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2)
        print(f"已保存评估指标至 {args.metrics_file}")

if __name__ == '__main__':
    main()
