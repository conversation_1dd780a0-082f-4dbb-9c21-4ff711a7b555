{"question_id": "2hop__50910_177869", "prediction": "the answer is: <PERSON>"}
{"question_id": "2hop__20059_236755", "prediction": "So the answer is: Human Action: A Treatise on Economics"}
{"question_id": "2hop__511176_22458", "prediction": "the answer is: International Federation of Association Football"}
{"question_id": "3hop1__454441_55349_651302", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__373544_80420", "prediction": "the answer is: <PERSON>"}
{"question_id": "4hop1__199881_378185_282674_759393", "prediction": "The county seat of the county sharing a border with the county in which Miller Electric is headquartered is Norton."}
{"question_id": "2hop__39420_39345", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__358270_108549", "prediction": "The answer is: <PERSON><PERSON>."}
{"question_id": "4hop1__668178_765799_282674_759393", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__496961_554601", "prediction": "Kingscliff, New South Wales, Australia"}
{"question_id": "2hop__388467_110882", "prediction": "The birth date of the By Your Side performer is January 1, 1986."}
{"question_id": "2hop__606439_127399", "prediction": "So the answer is: unanswerable."}
{"question_id": "3hop1__847420_720378_15538", "prediction": "the answer is: 1984"}
{"question_id": "2hop__763380_74735", "prediction": "the answer is: Hey Jude"}
{"question_id": "2hop__623501_297043", "prediction": "The Mississippi River"}
{"question_id": "4hop1__146971_698949_157828_162309", "prediction": "starting at the 1992 Winter Games"}
{"question_id": "2hop__135029_712629", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__35566_17873", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__284789_52835", "prediction": "John D. Loudermilk"}
{"question_id": "2hop__6827_75783", "prediction": "the answer is: July 1, 2008."}
{"question_id": "2hop__36269_73244", "prediction": "So the answer is: Thaba Putsoa"}
{"question_id": "2hop__703767_37168", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop1__831499_228453_10972", "prediction": "the answer is: None"}
{"question_id": "3hop1__241001_568433_51423", "prediction": "The Malbork Castle"}
{"question_id": "2hop__107185_64006", "prediction": "So the answer is: 30% to 65%"}
{"question_id": "2hop__88834_356793", "prediction": "the answer is: Peter Marc Jacobson"}
{"question_id": "2hop__75781_512508", "prediction": "the Manhattan Project"}
{"question_id": "2hop__551235_310309", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__156700_63853", "prediction": "unanswerable"}
{"question_id": "2hop__249867_557232", "prediction": "the answer is: Kingdom of Saudi Arabia"}
{"question_id": "4hop1__152146_5274_458768_33637", "prediction": "unanswerable"}
{"question_id": "3hop1__480375_709625_84283", "prediction": "So the answer is: Jay Z"}
{"question_id": "3hop2__72083_92991_76291", "prediction": "2010"}
{"question_id": "3hop2__668732_467331_162182", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop1__58323_375563_161848_61344", "prediction": "Asia"}
{"question_id": "2hop__72660_319330", "prediction": "the answer is: Jessie Woodrow Wilson Sayre"}
{"question_id": "3hop2__782397_223623_162182", "prediction": "So the answer is: South Central Coast."}
{"question_id": "2hop__536283_67465", "prediction": "The performer of Watching Movies released Best Day Ever in 2011. So the answer is: March 11, 2011."}
{"question_id": "2hop__149710_108549", "prediction": "unanswerable"}
{"question_id": "2hop__15416_58040", "prediction": "So the answer is: San Diego"}
{"question_id": "3hop1__662618_339990_54675", "prediction": "the answer is: Tualatin Mountains"}
{"question_id": "2hop__30905_189318", "prediction": "National War College"}
{"question_id": "2hop__30390_92972", "prediction": "the European economy had collapsed"}
{"question_id": "3hop1__21282_692112_236857", "prediction": "The answer is: Poland."}
{"question_id": "3hop1__707569_228453_10972", "prediction": "the answer is: 1970s"}
{"question_id": "3hop1__104762_24918_24991", "prediction": "So the answer is: the Soviet flag"}
{"question_id": "2hop__147171_27537", "prediction": "So the answer is: to participate in a conclave following Pope Pius XII's death"}
{"question_id": "3hop1__836699_15840_36002", "prediction": "the answer is: 16-bit architecture, enhanced graphics capabilities, and superior sound quality"}
{"question_id": "3hop2__89048_228_66294", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop1__786632_831637_91775", "prediction": "So the answer is: Sea, Air, and Land."}
{"question_id": "2hop__43283_86694", "prediction": "The New York Yankees"}
{"question_id": "3hop1__446612_160545_34751", "prediction": "So the answer is: None"}
{"question_id": "4hop3__238811_88460_30152_20999", "prediction": "So the answer is: defeated Portuguese forces"}
{"question_id": "2hop__143650_152023", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__682872_516436", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__658472_5385", "prediction": "The school where Gene Ball was educated has 24 people working there."}
{"question_id": "4hop1__813171_153080_159767_81096", "prediction": "The race number 102 was won by Richard Petty."}
{"question_id": "3hop2__6989_37759_74563", "prediction": "the answer is 1630"}
{"question_id": "2hop__853227_68633", "prediction": "So the answer is: No Answer."}
{"question_id": "3hop1__652852_2053_5289", "prediction": "Oriole Records"}
{"question_id": "3hop2__92991_38242_76291", "prediction": "1946"}
{"question_id": "3hop1__516535_834494_34088", "prediction": "The answer is: Tucson Raceway Park"}
{"question_id": "3hop2__88342_93066_47738", "prediction": "So the answer is: 2009"}
{"question_id": "4hop2__9988_158279_70784_79935", "prediction": "So the answer is: 1947"}
{"question_id": "2hop__451574_120065", "prediction": "the answer is: unanswerable."}
{"question_id": "4hop1__152562_5274_458768_33632", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__789398_74735", "prediction": "the answer is: Hey Jude"}
{"question_id": "2hop__998_25839", "prediction": "So the answer is: roughly around 500 milliamps"}
{"question_id": "2hop__632870_22402", "prediction": "the answer is: 25,000"}
{"question_id": "4hop3__531933_836463_161616_77103", "prediction": "the answer is: 1839"}
{"question_id": "3hop1__131829_39743_24526", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__135668_561444", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__516535_834494_33939", "prediction": "the answer is: 68,479"}
{"question_id": "3hop1__379231_40769_64047", "prediction": "April 2012"}
{"question_id": "2hop__814856_110882", "prediction": "the answer is: June 15"}
{"question_id": "2hop__748615_123956", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__109242_113442", "prediction": "So the answer is: 2005."}
{"question_id": "2hop__511454_120259", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__671542_665330_47686", "prediction": "Toronto Coach Terminal"}
{"question_id": "2hop__860241_766393", "prediction": "the Prix Goncourt"}
{"question_id": "4hop2__161602_426860_88460_21034", "prediction": "the answer is: 2014"}
{"question_id": "2hop__442516_595885", "prediction": "Myanmar"}
{"question_id": "3hop1__398232_326948_78782", "prediction": "General Edward Pakenham."}
{"question_id": "3hop1__106042_64399_52278", "prediction": "So the answer is: Pope John XXIII"}
{"question_id": "3hop1__88707_91850_685675", "prediction": "The player who hit the first home run at the new location of NY Yankees home games was on the Seattle Mariners. So the answer is: Seattle Mariners."}
{"question_id": "2hop__71755_463572", "prediction": "Orion Records"}
{"question_id": "3hop1__106094_443779_52195", "prediction": "the president of Timor is Susilo Bambang Yudhoyono."}
{"question_id": "2hop__71715_50429", "prediction": "59,210,000"}
{"question_id": "2hop__829474_123956", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__24648_192417", "prediction": "The National Football League is an example of a major league sports franchise. The answer is the National Football League."}
{"question_id": "2hop__20256_71302", "prediction": "the answer is: Vito Corleone"}
{"question_id": "3hop1__527372_339990_54675", "prediction": "So the answer is: Tualatin Mountains"}
{"question_id": "3hop1__178435_547811_80702", "prediction": "the answer is: Rialto Bridge"}
{"question_id": "3hop2__304722_526346_63959", "prediction": "So the answer is: December 1st, 2010."}
{"question_id": "2hop__731584_700117", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__244193_461106", "prediction": "unanswerable"}
{"question_id": "4hop1__44149_32392_823060_610794", "prediction": "The answer is: Richland County."}
{"question_id": "3hop1__222497_309482_89834", "prediction": "So the answer is: None."}
{"question_id": "2hop__335899_124498", "prediction": "The father of the performer of Qui de noux deux is Louis Chedid."}
{"question_id": "3hop1__409517_547811_41132", "prediction": "unanswerable"}
{"question_id": "4hop1__145494_698949_157828_162309", "prediction": "So the answer is: 2016"}
{"question_id": "2hop__144784_623159", "prediction": "So the answer is: Ural Oblast"}
{"question_id": "3hop1__53690_161697_85460", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__445963_6063", "prediction": "the answer is: 4,255."}
{"question_id": "2hop__567588_67465", "prediction": "March 11, 2011"}
{"question_id": "4hop3__373866_5189_31809_86687", "prediction": "the answer is: 1945"}
{"question_id": "2hop__498251_126089", "prediction": "The GDR Border Troops"}
{"question_id": "2hop__55463_19033", "prediction": "So the answer is: Nuevo Laredo"}
{"question_id": "3hop2__127483_76623_10557", "prediction": "So the answer is: Latin"}
{"question_id": "3hop1__315504_629431_64412", "prediction": "The answer is: February 11, 1929."}
{"question_id": "2hop__32887_235620", "prediction": "Emperor Zhaozong of Tang"}
{"question_id": "3hop1__639955_834494_34099", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__499577_617190", "prediction": "The answer is: Angela Merkel."}
{"question_id": "2hop__24976_161105", "prediction": "The organizers wanted to arrest Yeltsin narrowly survived impeachment by 72 votes."}
{"question_id": "2hop__75207_365216", "prediction": "the Talca Province of Chile's Maule Region"}
{"question_id": "2hop__197470_271394", "prediction": "unanswerable"}
{"question_id": "2hop__159903_154896", "prediction": "unanswerable"}
{"question_id": "2hop__157602_21567", "prediction": "So the answer is: John Kukuzelis"}
{"question_id": "2hop__131818_161450", "prediction": "the final answer is: Pakistani territory."}
{"question_id": "2hop__6735_6733", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__821197_368148", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop1__130276_59747_211319_557671", "prediction": "So the answer is: Cobb County"}
{"question_id": "2hop__789565_416401", "prediction": "Xiaoping"}
{"question_id": "4hop3__373573_132409_223216_35031", "prediction": "the second-largest"}
{"question_id": "3hop1__136129_87694_64412", "prediction": "So the answer is: unanswerable."}
{"question_id": "3hop1__178435_547811_41132", "prediction": "unanswerable"}
{"question_id": "3hop2__145209_20335_9331", "prediction": "Habsburg Monarchy"}
{"question_id": "2hop__310456_846599", "prediction": "So the answer is: Alexander the Great"}
{"question_id": "4hop1__814776_765799_282674_759393", "prediction": "So the answer is: Elmo."}
{"question_id": "3hop1__106688_160713_77246", "prediction": "The guiding religion is Hinduism."}
{"question_id": "2hop__89991_58067", "prediction": "A.P.J. Abdul Kalam"}
{"question_id": "3hop1__489567_443779_52195", "prediction": "The answer is: Yushchenko."}
{"question_id": "2hop__75714_21969", "prediction": "So the answer is: 1786."}
{"question_id": "2hop__374495_68633", "prediction": "So the answer is: Thomas Bach"}
{"question_id": "4hop1__105401_17130_70784_79935", "prediction": "unanswerable"}
{"question_id": "3hop1__857_846_7874", "prediction": "Beijing Knicks"}
{"question_id": "3hop1__226638_538202_84283", "prediction": "So the answer is: MGM Records"}
{"question_id": "2hop__6750_6733", "prediction": "the answer is: Balkan"}
{"question_id": "2hop__72487_510545", "prediction": "the answer is: Jennifer Connelly"}
{"question_id": "2hop__83405_158262", "prediction": "the answer is: 1846-06-27"}
{"question_id": "2hop__29873_679424", "prediction": "the answer is: unanswerable."}
{"question_id": "4hop1__606685_508773_85832_745702", "prediction": "the answer is: Gaspar de Portolu00e0."}
{"question_id": "2hop__39231_17335", "prediction": "The answer is: 323 BC"}
{"question_id": "4hop2__567956_14670_8987_8974", "prediction": "So the answer is: Warsaw"}
{"question_id": "4hop1__88342_75218_128008_87812", "prediction": "So the answer is: Thursday, March 29."}
{"question_id": "4hop1__94201_642284_131926_89261", "prediction": "the Mississippi River Delta"}
{"question_id": "3hop1__305566_568433_51423", "prediction": "The Malbork Castle."}
{"question_id": "4hop3__193820_466199_695123_72134", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop2__71753_648517_70784_61381", "prediction": "the answer is: unanswerable."}
{"question_id": "2hop__723404_58556", "prediction": "So the answer is: FSMA"}
{"question_id": "2hop__864201_80026", "prediction": "So the answer is: Julie Dawn Cole"}
{"question_id": "2hop__434218_29898", "prediction": "So the answer is: CW"}
{"question_id": "3hop1__106042_64399_53006", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__483634_720378_15538", "prediction": "the answer is: 1984"}
{"question_id": "2hop__693157_131890", "prediction": "the Mississippi River"}
{"question_id": "4hop1__370129_621192_10659_42311", "prediction": "So the answer is: Medical University of Warsaw."}
{"question_id": "3hop1__128865_30587_83479", "prediction": "So the answer is: to block the British supply route to the Mediterranean"}
{"question_id": "4hop1__842143_153080_159767_81096", "prediction": "the answer is: Michael Andretti"}
{"question_id": "3hop1__19768_19788_19761", "prediction": "So the answer is: Sufi missionaries"}
{"question_id": "2hop__203955_25719", "prediction": "The answer is: a)."}
{"question_id": "3hop1__19768_19788_15107", "prediction": "the answer is: 48%"}
{"question_id": "4hop1__570103_49925_13759_736921", "prediction": "the answer is: Saxony-Anhalt, Germany"}
{"question_id": "2hop__412633_15781", "prediction": "Hayek"}
{"question_id": "2hop__61714_52026", "prediction": "Niagara"}
{"question_id": "3hop2__125104_90098_10557", "prediction": "Sylvester the Great"}
{"question_id": "3hop2__127483_79978_10557", "prediction": "So the answer is: Latin"}
{"question_id": "4hop1__152146_5274_458768_33677", "prediction": "The answer is: unanswerable."}
{"question_id": "4hop2__160585_14670_8987_8974", "prediction": "So the answer is: Czechoslovakia"}
{"question_id": "2hop__7046_44085", "prediction": "the answer is: The Mickey Mouse Club"}
{"question_id": "2hop__580231_162399", "prediction": "the answer is: 15 December 1950"}
{"question_id": "2hop__166820_52835", "prediction": "John D. Loudermilk"}
{"question_id": "2hop__3737_13529", "prediction": "the answer is: June 1982"}
{"question_id": "2hop__673655_316668", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__81268_85407", "prediction": "the answer is: 260,897."}
{"question_id": "4hop1__94201_642284_131926_13165", "prediction": "The Treaty of Paris"}
{"question_id": "2hop__731228_609866", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__818692_113442", "prediction": "the answer is: August 13, 1896"}
{"question_id": "3hop1__617062_127905_48717", "prediction": "unanswerable"}
{"question_id": "2hop__326404_120065", "prediction": "So the answer is: unanswerable."}
{"question_id": "3hop1__823336_228453_10972", "prediction": "the answer is: 1970s"}
{"question_id": "3hop1__109185_720914_41132", "prediction": "unanswerable"}
{"question_id": "4hop1__726675_508773_85832_745702", "prediction": "Sebastian Cabot"}
{"question_id": "3hop1__390489_502967_84283", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__68322_510545", "prediction": "So the answer is: Jennifer Connelly."}
{"question_id": "3hop2__304722_398767_63959", "prediction": "The answer is: unanswerable."}
{"question_id": "3hop1__475351_160713_77246", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop2__855821_223623_162182", "prediction": "the answer is: South Central Coast"}
{"question_id": "2hop__5190_64006", "prediction": "30% to 65%"}
{"question_id": "3hop1__145194_160545_34751", "prediction": "unanswerable"}
{"question_id": "3hop2__132957_132967_40768", "prediction": "1981"}
{"question_id": "3hop1__622497_160088_85460", "prediction": "unanswerable"}
{"question_id": "3hop1__106313_443779_52195", "prediction": "Abdoulaye Wade"}
{"question_id": "2hop__223655_463572", "prediction": "the answer is: MGM Records."}
{"question_id": "2hop__160092_80144", "prediction": "ivotin Kostunica"}
{"question_id": "3hop1__398232_326948_10972", "prediction": "the answer is: 1970s"}
{"question_id": "2hop__804417_126089", "prediction": "the answer is: Pope John XXIII"}
{"question_id": "2hop__543853_124498", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__57209_67668", "prediction": "the actor who plays Barney in \"How I Met Your Mother\" plays Nightwing / Dick Grayson in Batman Under the Red Hood."}
{"question_id": "2hop__13106_158105", "prediction": "So the answer is: ease of use and enhanced support for Plug and Play (PnP)"}
{"question_id": "2hop__50199_59409", "prediction": "So the answer is: James Cottriall"}
{"question_id": "3hop1__157807_19788_15107", "prediction": "the answer is: 48"}
{"question_id": "2hop__92385_2072", "prediction": "the answer is: 101"}
{"question_id": "2hop__85931_108632", "prediction": "So the answer is: Mario Puzo"}
{"question_id": "2hop__159816_37168", "prediction": "The answer is: unanswerable."}
{"question_id": "4hop3__275416_24325_156850_10557", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__763840_144857", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__475808_74481", "prediction": "the producer of Mistress"}
{"question_id": "2hop__150866_11402", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__44652_165532", "prediction": "So the answer is: Amy Madigan"}
{"question_id": "2hop__25797_990", "prediction": "So the answer is: third generation."}
{"question_id": "2hop__153573_44085", "prediction": "The Mighty B"}
{"question_id": "3hop1__374018_160713_77246", "prediction": "unanswerable"}
{"question_id": "2hop__504464_708278", "prediction": "unanswerable"}
{"question_id": "3hop1__715607_792411_51423", "prediction": "So the answer is: Casa Loma"}
{"question_id": "2hop__160292_23241", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__132469_533882", "prediction": "Honda/Acura"}
{"question_id": "2hop__280583_229757", "prediction": "the answer is: Will Arnett"}
{"question_id": "3hop1__532517_214799_259594", "prediction": "So the answer is: Germany."}
{"question_id": "2hop__431934_30351", "prediction": "So the answer is: Austrian government."}
{"question_id": "3hop1__458916_665330_51423", "prediction": "So the answer is: Casa Loma"}
{"question_id": "4hop1__56270_53706_795904_580996", "prediction": "New Mexico"}
{"question_id": "2hop__135652_669373", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop1__51465_53706_795904_580996", "prediction": "So the answer is: San Diego"}
{"question_id": "4hop3__316459_41402_599212_13584", "prediction": "unanswerable"}
{"question_id": "2hop__809785_606637", "prediction": "the answer is: Hollywood Records"}
{"question_id": "4hop2__161602_474028_88460_18966", "prediction": "Han Chinese emigrants"}
{"question_id": "2hop__394720_120259", "prediction": "918"}
{"question_id": "3hop1__30240_740597_41132", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop1__84887_538202_84283", "prediction": "The Warner Music Group."}
{"question_id": "3hop1__408237_400692_51423", "prediction": "the answer is: Casa Loma"}
{"question_id": "2hop__77878_189357", "prediction": "So the answer is: Harvard College."}
{"question_id": "2hop__398344_54166", "prediction": "the answer is: Four."}
{"question_id": "3hop1__637555_339990_15538", "prediction": "the answer is 1984."}
{"question_id": "4hop2__161602_426860_88460_18966", "prediction": "unanswerable"}
{"question_id": "4hop3__322354_466199_695123_72134", "prediction": "So the answer is: 1829."}
{"question_id": "2hop__685280_510757", "prediction": "Peace Corps"}
{"question_id": "2hop__194532_57594", "prediction": "The LeMat revolver."}
{"question_id": "3hop1__751782_2053_5289", "prediction": "Oriole Records"}
{"question_id": "2hop__123545_47134", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__48776_807969", "prediction": "the answer is: Liverpool"}
{"question_id": "4hop3__839787_88460_30152_20999", "prediction": "Geopolitical Positioning"}
{"question_id": "3hop1__695430_831637_91775", "prediction": "Sea, Air, and Land"}
{"question_id": "2hop__538735_3299", "prediction": "the answer is: Season Five"}
{"question_id": "4hop1__708184_153080_33897_81096", "prediction": "the answer is: Ryan Hunter-Reay"}
{"question_id": "3hop1__273571_834494_34088", "prediction": "The Great Salt Lake"}
{"question_id": "2hop__607517_161450", "prediction": "So the answer is: Iran"}
{"question_id": "2hop__10620_49084", "prediction": "The answer is Liam Thomas Garrigan."}
{"question_id": "3hop1__210639_147339_47686", "prediction": "The Toronto Coach Terminal"}
{"question_id": "4hop3__773800_152874_161616_77103", "prediction": "the answer is: 1839"}
{"question_id": "3hop1__837871_831637_91775", "prediction": "So the answer is: unanswerable."}
{"question_id": "4hop2__161602_474028_88460_86452", "prediction": "the answer is: None"}
{"question_id": "4hop1__58323_375563_161848_28750", "prediction": "The University of New Zealand"}
{"question_id": "2hop__46065_417697", "prediction": "The car manufacturer owning the naming rights to titans stadium is a type of sedan. The 2001 Nissan Altima, 1N4DL01D81C212547 is the VIN of a blue rental car belonging to Alamo Rent a Car, that was found in the Portland International Jetport parking lot, following the September 11, 2001 attacks. So the answer is: sedan."}
{"question_id": "2hop__73719_510545", "prediction": "Jennifer Connelly"}
{"question_id": "3hop1__9285_5188_63702", "prediction": "So the answer is: 1 September 1939"}
{"question_id": "3hop1__131820_59747_90139", "prediction": "the answer is: Kim Zolciak"}
{"question_id": "2hop__557284_160249", "prediction": "So the answer is: 323–272 BC"}
{"question_id": "3hop1__351696_42197_18397", "prediction": "So the answer is: Beijing"}
{"question_id": "2hop__363214_590899", "prediction": "The answer is: Randolph County."}
{"question_id": "3hop2__132957_295815_40768", "prediction": "1981"}
{"question_id": "2hop__25788_25855", "prediction": "So the answer is: PowerShare"}
{"question_id": "4hop1__436202_378185_282674_759393", "prediction": "The answer is: Rich Mountain."}
{"question_id": "2hop__635185_141338", "prediction": "The answer is: Morgan Stanley"}
{"question_id": "3hop2__132957_133292_40768", "prediction": "1981"}
{"question_id": "2hop__73760_712629", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__365934_87694_64412", "prediction": "So the answer is: February 11, 1929."}
{"question_id": "3hop1__131783_131926_90707", "prediction": "Mississippi River Delta"}
{"question_id": "3hop2__304722_230978_63959", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__783711_503371_21711", "prediction": "15th century"}
{"question_id": "4hop3__638064_88460_30152_20999", "prediction": "forced"}
{"question_id": "2hop__557496_57594", "prediction": "the answer is: LeMat revolver"}
{"question_id": "2hop__818302_25719", "prediction": "The answer is: c. 98."}
{"question_id": "3hop2__30152_105895_20999", "prediction": "So the answer is: repelled invaders like Portugal"}
{"question_id": "2hop__135651_669373", "prediction": "the University of Miami"}
{"question_id": "2hop__89403_122023", "prediction": "So the answer is: Luitpold Gymnasium."}
{"question_id": "3hop1__389069_132457_51423", "prediction": "So the answer is: Casa Loma."}
{"question_id": "3hop1__140514_2053_5289", "prediction": "EmArcy Records"}
{"question_id": "2hop__373759_136043", "prediction": "Julian Lennon"}
{"question_id": "3hop1__765565_326948_86925", "prediction": "So the answer is: unanswerable."}
{"question_id": "4hop1__264443_49925_13759_736921", "prediction": "So the answer is: Saxony-Anhalt, Germany"}
{"question_id": "4hop3__130404_29339_508306_70744", "prediction": "So the answer is: Oregon State University"}
{"question_id": "2hop__370911_589237", "prediction": "the answer is: Joan Erikson"}
{"question_id": "4hop1__15567_59747_211319_557671", "prediction": "So the answer is: Hazzard County"}
{"question_id": "2hop__678516_179720", "prediction": "the answer is: Joan Erikson"}
{"question_id": "4hop1__161605_32392_823060_610794", "prediction": "So the answer is: Richland County"}
{"question_id": "2hop__53937_92673", "prediction": "The North Pole."}
{"question_id": "4hop1__21282_692112_214799_259594", "prediction": "GER"}
{"question_id": "4hop1__61681_53706_795904_580996", "prediction": "unanswerable"}
{"question_id": "4hop1__37608_32392_823060_610794", "prediction": "So the answer is: Randolph County"}
{"question_id": "3hop2__20335_8533_9331", "prediction": "the kingdom acquiring some Thuringian territory"}
{"question_id": "3hop1__145411_443779_52195", "prediction": "the answer is: Aung San Suu Kyi"}
{"question_id": "2hop__87580_7298", "prediction": "unanswerable"}
{"question_id": "2hop__466700_214359", "prediction": "the answer is: Aaron Benward"}
{"question_id": "2hop__85958_87295", "prediction": "the answer is: Queen Dowager Clarisse Renaldi"}
{"question_id": "2hop__82910_75184", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__864101_135373", "prediction": "unanswerable"}
{"question_id": "4hop3__129754_29339_508306_70744", "prediction": "the school with Fort Hill"}
{"question_id": "4hop3__160230_470287_128875_72134", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__657913_88628", "prediction": "the producer of Big Jim McLain"}
{"question_id": "2hop__505860_650613", "prediction": "the answer is: Exeter College, Oxford."}
{"question_id": "2hop__93145_54024", "prediction": "unanswerable"}
{"question_id": "2hop__156658_155922", "prediction": "The Parramatta River is the main tributary of Sydney Harbour, a branch of Port Jackson. The answer is: Sydney Harbour."}
{"question_id": "2hop__91713_289940", "prediction": "the answer is: Houston Texans"}
{"question_id": "3hop2__140194_49541_51068", "prediction": "the answer is: Julie Andrews."}
{"question_id": "3hop2__573858_613770_7713", "prediction": "about four centuries"}
{"question_id": "3hop1__337705_132457_51423", "prediction": "So the answer is: Casa Loma."}
{"question_id": "2hop__21104_16254", "prediction": "So the answer is: 158."}
{"question_id": "3hop2__57186_89048_66294", "prediction": "So the answer is: Audrey Hepburn"}
{"question_id": "2hop__61714_80069", "prediction": "The two characters are revealed to be dating in the opener of Season 4."}
{"question_id": "2hop__135993_160249", "prediction": "So the answer is: 323u2013272 BC."}
{"question_id": "3hop1__346812_15840_36014", "prediction": "the answer is: five games per year"}
{"question_id": "3hop1__312146_720914_41132", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__776377_857193", "prediction": "the answer is: Delaware River"}
{"question_id": "2hop__34595_160249", "prediction": "323–272 BC"}
{"question_id": "2hop__826864_17335", "prediction": "unanswerable"}
{"question_id": "3hop1__68802_89752_75165", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__435184_84856", "prediction": "So the answer is: January 20, 2017"}
{"question_id": "3hop1__765847_831637_91775", "prediction": "Sea, Air, and Land"}
{"question_id": "2hop__135822_46621", "prediction": "the answer is: 3rd century BC"}
{"question_id": "2hop__693444_220151", "prediction": "the answer is: Joan Erikson"}
{"question_id": "2hop__82270_177869", "prediction": "Helen Pitts Douglass"}
{"question_id": "3hop1__63037_567566_84283", "prediction": "the answer is: Sony Music Entertainment"}
{"question_id": "2hop__640262_122868", "prediction": "the answer is: University of Mississippi"}
{"question_id": "4hop1__341156_49925_13759_736921", "prediction": "So the answer is: Brandenburg."}
{"question_id": "3hop1__465684_160545_60577", "prediction": "the answer is: Namibia"}
{"question_id": "4hop3__39836_29339_508306_70744", "prediction": "the University of South Carolina"}
{"question_id": "2hop__142621_225454", "prediction": "The Right Stuff Records"}
{"question_id": "3hop1__353661_629431_124169", "prediction": "the answer is: 1952"}
{"question_id": "2hop__819974_129669", "prediction": "the answer is: unanswerable."}
{"question_id": "2hop__817096_15463", "prediction": "116"}
{"question_id": "3hop1__389761_567566_84283", "prediction": "the answer is: Warner Music Group"}
{"question_id": "2hop__472486_97805", "prediction": "The operator of the Fabian Wrede-class training ship served in World War II."}
{"question_id": "4hop3__257340_88460_30152_20999", "prediction": "So the answer is: unanswerable"}
{"question_id": "4hop3__857_846_326964_7713", "prediction": "So the answer is: 97"}
{"question_id": "3hop1__139862_2053_52946", "prediction": "So the answer is: unanswerable."}
{"question_id": "3hop1__354480_834494_34099", "prediction": "the answer is: four - year"}
{"question_id": "3hop2__93066_88342_47738", "prediction": "So the answer is: 2009"}
{"question_id": "3hop2__304722_667199_63959", "prediction": "So the answer is: 2010-December-1."}
{"question_id": "2hop__557411_652060", "prediction": "The Nanny Reunion: A Nosh to Remember"}
{"question_id": "4hop1__802394_153080_33897_81096", "prediction": "Michael Andretti"}
{"question_id": "2hop__106864_80460", "prediction": "So the answer is: unanswerable."}
{"question_id": "3hop2__159979_30587_44003", "prediction": "So the answer is: The Florida Straits"}
{"question_id": "3hop1__691197_15840_36002", "prediction": "So the answer is: 16-bit architecture, enhanced graphics capabilities, and superior sound quality"}
{"question_id": "4hop2__161324_14670_8987_8974", "prediction": "So the answer is: Paraguay"}
{"question_id": "3hop1__135659_87694_124169", "prediction": "So the answer is: 1828."}
{"question_id": "2hop__230022_68489", "prediction": "the actor who played Michael Knight play on Young and Restless is Snapper Foster."}
{"question_id": "3hop1__23058_20608_36327", "prediction": "The New Africa"}
{"question_id": "2hop__771477_149987", "prediction": "So the answer is: Greek"}
{"question_id": "3hop1__131783_131926_89261", "prediction": "So the answer is: via its delta"}
{"question_id": "3hop1__102087_214799_259594", "prediction": "GER"}
{"question_id": "3hop1__320697_547811_41132", "prediction": "the answer is: 288."}
{"question_id": "2hop__701895_752697", "prediction": "So the answer is: Tamaulipas"}
{"question_id": "2hop__47257_43786", "prediction": "So the answer is: The Sun"}
{"question_id": "3hop1__862117_792411_51423", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop3__373866_5189_38229_86687", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop1__462960_160545_62931", "prediction": "the answer is: Koh Phi Phi Island"}
{"question_id": "3hop1__458916_665330_47686", "prediction": "The Toronto Coach Terminal"}
{"question_id": "3hop1__161433_33952_34099", "prediction": "the answer is: four - year"}
{"question_id": "2hop__301011_820181", "prediction": "the answer is: Randolph County"}
{"question_id": "4hop3__316459_41402_146223_13584", "prediction": "Real Madrid is in Spain"}
{"question_id": "3hop1__131783_131926_87157", "prediction": "So the answer is: None"}
{"question_id": "2hop__84937_21969", "prediction": "between 1788 and 1795"}
{"question_id": "2hop__80860_510545", "prediction": "So the answer is: Joan Erikson"}
{"question_id": "4hop2__161602_426860_88460_21062", "prediction": "yes"}
{"question_id": "4hop1__343844_17130_70784_61381", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__29606_600504", "prediction": "The field of work was the person who discovered the mathematical relationship between peaks and curves of light at different temperatures in physics. So the answer is: physics."}
{"question_id": "4hop1__726152_153080_159767_81096", "prediction": "So the answer is: Mario Andretti"}
{"question_id": "4hop3__387712_132409_146285_35031", "prediction": "the fifth largest"}
{"question_id": "2hop__706637_93362", "prediction": "the answer is: Yeh Raaste Hain Pyaar Ke."}
{"question_id": "2hop__848156_130869", "prediction": "The answer is: New Zealand."}
{"question_id": "4hop1__130508_32392_823060_610794", "prediction": "The County of Richland is 380 miles away from the County of Darlington."}
{"question_id": "4hop1__638706_765799_282674_759393", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__39954_80940", "prediction": "So the answer is: Myitkyina National Park"}
{"question_id": "3hop1__818422_160545_34751", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__82713_84616", "prediction": "the answer is: Best Performance by a Leading Actress in a Play"}
{"question_id": "2hop__754826_7292", "prediction": "So the answer is: James Otto"}
{"question_id": "2hop__463884_807572", "prediction": "So the answer is: Maricopa County"}
{"question_id": "4hop1__88342_49853_128008_86588", "prediction": "the longest homer in the history of the league where the team with the most titles from the event after which they give out the MLB MVP award plays is about 575 feet ( 175 m), by Babe Ruth, to straightaway center field at Tiger Stadium (then called Navin Field and before the double - deck), which landed nearly across the intersection of Trumbull and Cherry. So the answer is: 575 feet ( 175 m ), by Babe Ruth, to straightaway center field at Tiger Stadium (then called Navin Field and before the double - deck), which landed nearly across the intersection of Trumbull and Cherry."}
{"question_id": "2hop__6827_79845", "prediction": "The number of ATMs in the bank that bought FleetBoston Financial is 15,900."}
{"question_id": "3hop1__354480_834494_34109", "prediction": "So the answer is: Illinois"}
{"question_id": "2hop__86689_728109", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__85455_158105", "prediction": "versatility within the entertainment industry"}
{"question_id": "4hop3__175072_88460_30152_20999", "prediction": "unanswerable"}
{"question_id": "2hop__55966_162341", "prediction": "So the answer is: Oscar for Best Actor."}
{"question_id": "3hop1__558449_503371_21711", "prediction": "the answer is: None"}
{"question_id": "2hop__822965_165304", "prediction": "the answer is: 376."}
{"question_id": "2hop__157737_46621", "prediction": "the answer is: 3rd century BC"}
{"question_id": "2hop__92716_319330", "prediction": "So the answer is: Jessie Woodrow Wilson Sayre"}
{"question_id": "2hop__311694_129669", "prediction": "the answer is: Texas"}
{"question_id": "2hop__92900_512508", "prediction": "The person credited with the discovery of the neutron is a participant in the Manhattan Project. So the answer is: Manhattan Project."}
{"question_id": "2hop__131061_54221", "prediction": "the answer is: None"}
{"question_id": "4hop1__749363_49925_13759_736921", "prediction": "So the answer is: Italy."}
{"question_id": "2hop__815205_590407", "prediction": "So the answer is: Bethesda, Maryland."}
{"question_id": "2hop__73717_653666", "prediction": "So the answer is: Lucy Mack Smith."}
{"question_id": "4hop2__71753_685934_70784_61381", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__161872_21567", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop1__662413_42197_18397", "prediction": "In a speech in December 1950"}
{"question_id": "2hop__83938_5666", "prediction": "the answer is: Boris Yeltsin"}
{"question_id": "3hop1__742780_567566_84283", "prediction": "the answer is: Warner Music Group"}
{"question_id": "3hop1__36497_78276_68042", "prediction": "the answer is: 2010"}
{"question_id": "2hop__6827_86319", "prediction": "So the answer is: Brian Thomas Moynihan"}
{"question_id": "3hop2__92991_38326_76291", "prediction": "So the answer is: January 2015"}
{"question_id": "2hop__852657_155922", "prediction": "the answer is: Sydney Harbour"}
{"question_id": "2hop__685930_110949", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__773623_87694_124169", "prediction": "the answer is: 1952"}
{"question_id": "2hop__403580_37168", "prediction": "So the answer is: unanswerable"}
{"question_id": "2hop__24973_5674", "prediction": "So the answer is: Yeltsin"}
{"question_id": "4hop1__88342_49853_128008_46748", "prediction": "the answer is: 26"}
{"question_id": "4hop3__152056_698586_1926_54362", "prediction": "unanswerable"}
{"question_id": "2hop__81379_84616", "prediction": "So the answer is: 448."}
{"question_id": "2hop__61714_53995", "prediction": "the highly anticipated, hour long episode, Niagara"}
{"question_id": "2hop__20256_74481", "prediction": "the answer is: Vito Corleone"}
{"question_id": "4hop2__9988_158279_70784_61381", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__424294_19033", "prediction": "The author of A Lion's Tale: Around the World in Spandex won in 2008 In the Footsteps of Marco Polo. So the answer is: In the Footsteps of Marco Polo."}
{"question_id": "2hop__176_51164", "prediction": "September 3, 1783"}
{"question_id": "3hop1__398232_326948_59893", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__29349_92763", "prediction": "unanswerable"}
{"question_id": "3hop1__818422_160545_60577", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__654651_55349_651302", "prediction": "The answer is: unanswerable."}
{"question_id": "4hop1__151903_5274_458768_33633", "prediction": "The answer is: August 3, 1769."}
{"question_id": "4hop2__71753_158279_70784_79935", "prediction": "So the answer is: unanswerable."}
{"question_id": "2hop__389778_78303", "prediction": "unanswerable"}
{"question_id": "4hop1__324759_378185_282674_759393", "prediction": "The answer is: Yuma."}
{"question_id": "2hop__857585_14251", "prediction": "the answer is: George Robey's line \"Archibald, certainly not!\")."}
{"question_id": "2hop__217011_80026", "prediction": "the answer is: Philip Seymour Hoffman"}
{"question_id": "2hop__96411_159054", "prediction": "unanswerable"}
{"question_id": "2hop__669837_31270", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__13986_161674", "prediction": "October"}
{"question_id": "3hop1__103881_443779_52195", "prediction": "The final answer is: Susilo Bambang Yudhoyono."}
{"question_id": "4hop1__567737_141375_458768_33633", "prediction": "the answer is: August 3, 1769"}
{"question_id": "4hop1__455862_508773_85832_745702", "prediction": "So the answer is: Gaspar de Portolu00e0."}
{"question_id": "2hop__780652_93362", "prediction": "the answer is: Yeh Raaste Hain Pyaar Ke."}
{"question_id": "2hop__552607_126089", "prediction": "The answer is: unanswerable."}
{"question_id": "2hop__575797_91104", "prediction": "The experimental satellite that was the forerunner to communication satellite of the operator of INSAT-4CR was called ATS - 6 (Applications Technology Satellite - 6)."}
{"question_id": "3hop1__604991_339990_15538", "prediction": "the answer is: 1984"}
{"question_id": "4hop3__220945_88460_30152_20999", "prediction": "unanswerable"}
{"question_id": "3hop1__423001_503371_21711", "prediction": "the answer is: 256"}
{"question_id": "2hop__50432_56873", "prediction": "So the answer is: 1894-95."}
{"question_id": "4hop1__342858_131850_33952_34088", "prediction": "Tucson Raceway Park"}
{"question_id": "3hop2__69814_76873_64554", "prediction": "Japan"}
{"question_id": "3hop2__304722_372858_63959", "prediction": "the answer is: December 2016"}
{"question_id": "2hop__159741_40169", "prediction": "the answer is: April 1191"}
{"question_id": "2hop__175168_110222", "prediction": "The answer is: unanswerable."}
{"question_id": "4hop1__638988_17130_70784_61381", "prediction": "unanswerable"}
{"question_id": "4hop1__814349_624859_355213_203322", "prediction": "Randolph County"}
{"question_id": "2hop__77518_548781", "prediction": "unanswerable"}
{"question_id": "2hop__149983_108549", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop2__105527_14670_8987_8529", "prediction": "So the answer is: from 1943-1992"}
{"question_id": "4hop3__577443_88460_30152_20999", "prediction": "So the answer is: unanswerable"}
{"question_id": "3hop2__89854_36497_76291", "prediction": "2010"}
{"question_id": "3hop1__220023_503371_21711", "prediction": "the answer is: None"}
{"question_id": "3hop1__142044_538202_84283", "prediction": "Warner Music Group"}
{"question_id": "3hop1__743797_443779_52195", "prediction": "unanswerable"}
{"question_id": "2hop__31091_31122", "prediction": "the answer is: 160."}
{"question_id": "2hop__530579_13529", "prediction": "June 1982"}
{"question_id": "2hop__590631_110882", "prediction": "January 1, 1986"}
{"question_id": "2hop__5658_161105", "prediction": "The signer of the decree suspending the Communist Party of the Russian SFSR survived impeachment by 72 short of the 689 votes needed for a 2/3 majority. So the answer is 72."}
{"question_id": "3hop1__228_237521_291682", "prediction": "The Life of an Icon"}
{"question_id": "2hop__85325_88165", "prediction": "So the answer is: Keturah"}
{"question_id": "3hop1__672543_503371_21711", "prediction": "the answer is: None"}
{"question_id": "3hop2__92991_27069_76291", "prediction": "The 2014 elections gave the Republicans control of the Senate (and control of both houses of Congress) for the first time since the 109th Congress. Therefore, the answer is 2014."}
{"question_id": "2hop__38828_89399", "prediction": "the answer is: Dian Bachar"}
{"question_id": "3hop1__135392_87694_64412", "prediction": "the answer is: unanswerable"}
{"question_id": "3hop1__241085_42197_18397", "prediction": "So the answer is: unanswerable."}
{"question_id": "3hop1__103890_10659_67187", "prediction": "Gabriel Narutowicz"}
{"question_id": "3hop1__38738_78276_68042", "prediction": "2010"}
{"question_id": "2hop__198459_61845", "prediction": "unanswerable"}
{"question_id": "3hop1__649930_15840_36002", "prediction": "16-bit architecture, enhanced graphics capabilities, and superior sound quality"}
{"question_id": "2hop__749997_96912", "prediction": "the Ludwig von Mises Institute"}
{"question_id": "2hop__557743_92763", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__31995_8583", "prediction": "Germanic Goths & Iranic Sarmatians"}
{"question_id": "2hop__274529_5385", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__279729_20057", "prediction": "the answer is: Richard Stallman"}
{"question_id": "2hop__132472_120537", "prediction": "the answer is: 30 November 1999"}
{"question_id": "2hop__259228_793698", "prediction": "the answer is: None"}
{"question_id": "3hop2__79978_90098_10557", "prediction": "So the answer is: Latin"}
{"question_id": "2hop__59612_42352", "prediction": "So the answer is: 2 million"}
{"question_id": "4hop1__408432_32392_823060_610794", "prediction": "Randolph County"}
{"question_id": "2hop__820739_250752", "prediction": "So the answer is: unanswerable."}
{"question_id": "4hop2__5206_14670_8987_8529", "prediction": "between 1943-1992"}
{"question_id": "2hop__367336_97805", "prediction": "So the answer is: Winter War."}
{"question_id": "2hop__71871_494136", "prediction": "The answer is: Amy Poehler."}
{"question_id": "2hop__61714_89309", "prediction": "the highly anticipated, hour long episode"}
{"question_id": "2hop__43776_162341", "prediction": "Academy Award for Best Supporting Actor"}
{"question_id": "2hop__179322_83289", "prediction": "So the answer is: Matt Bennett"}
{"question_id": "4hop1__711307_49925_13759_736921", "prediction": "So the answer is: Bavaria"}
{"question_id": "3hop1__843207_755530_88011", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__108336_772156", "prediction": "unanswerable"}
{"question_id": "2hop__133241_33564", "prediction": "the answer is: unanswerable"}
{"question_id": "2hop__742831_156034", "prediction": "The Mississippi River."}
{"question_id": "2hop__507528_139339", "prediction": "the answer is: unanswerable."}
{"question_id": "2hop__757497_78606", "prediction": "the answer is: unanswerable"}
{"question_id": "4hop3__439878_88460_30152_20999", "prediction": "So the answer is: ousted under both the original and Restored Touggo Dynasties"}
{"question_id": "4hop1__105401_17130_70784_61381", "prediction": "unanswerable"}
{"question_id": "2hop__669456_108549", "prediction": "the answer is: Darin Morgan"}
{"question_id": "3hop1__689538_547811_80702", "prediction": "The Bridge of Sighs."}
{"question_id": "2hop__53175_548781", "prediction": "The number of unanswerable places in the graph."}
{"question_id": "4hop2__161602_426860_88460_126088", "prediction": "So the answer is: President Thein Sein"}
{"question_id": "3hop1__857_846_7769", "prediction": "unanswerable"}
