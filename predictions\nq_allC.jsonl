{"question_id": "single_nq_dev_6297", "prediction": "So the answer is: (I)."}
{"question_id": "single_nq_dev_6148", "prediction": "the answer is: monkey"}
{"question_id": "single_nq_dev_4648", "prediction": "The host of Who Wants to Be a Millionaire, <PERSON>, is a British television presenter."}
{"question_id": "single_nq_dev_2208", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_2250", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_5745", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1168", "prediction": "the answer is: June 2018"}
{"question_id": "single_nq_dev_6174", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_2263", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_272", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_3667", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5874", "prediction": "So the answer is: Cleveland Cavaliers"}
{"question_id": "single_nq_dev_109", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_5762", "prediction": "the answer is: Patrick"}
{"question_id": "single_nq_dev_3665", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_865", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_3280", "prediction": "the answer is: Howie Mandel , Mel B , Heidi Klum and Simon Cowell."}
{"question_id": "single_nq_dev_1700", "prediction": "So the answer is: The right atrium pumps blood to the ventricles"}
{"question_id": "single_nq_dev_6155", "prediction": "The answer is: [B]."}
{"question_id": "single_nq_dev_4725", "prediction": "the lack of social welfare and protection for working class"}
{"question_id": "single_nq_dev_1697", "prediction": "unanswerable"}
{"question_id": "single_nq_dev_445", "prediction": "the answer is: Transitional epithelium"}
{"question_id": "single_nq_dev_6093", "prediction": "[i]"}
{"question_id": "single_nq_dev_5618", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5459", "prediction": "the periproct"}
{"question_id": "single_nq_dev_3691", "prediction": "So the answer is: [a]."}
{"question_id": "single_nq_dev_6469", "prediction": "So the answer is: Langston Hughes"}
{"question_id": "single_nq_dev_5398", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1046", "prediction": "So the answer is: The Oakland Athletics"}
{"question_id": "single_nq_dev_596", "prediction": "The film was released on VHS and DVD on November 20, 2001. The extended cut of the film on VHS and DVD was released on October 29, 2002. So the answer is: November 20 , 2001."}
{"question_id": "single_nq_dev_2695", "prediction": "[A]."}
{"question_id": "single_nq_dev_5128", "prediction": "So the answer is: (B)."}
{"question_id": "single_nq_dev_4352", "prediction": "[i]"}
{"question_id": "single_nq_dev_1764", "prediction": "the answer is: Senusret I"}
{"question_id": "single_nq_dev_5236", "prediction": "So the answer is: Hinton Battle"}
{"question_id": "single_nq_dev_1724", "prediction": "So the answer is: (ii)"}
{"question_id": "single_nq_dev_2018", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5801", "prediction": "So the answer is: Taylor Lautner"}
{"question_id": "single_nq_dev_937", "prediction": "The year William Byron drove for JR Motorsports was 2006."}
{"question_id": "single_nq_dev_1659", "prediction": "The answer is: [a]."}
{"question_id": "single_nq_dev_3036", "prediction": "the Poles"}
{"question_id": "single_nq_dev_1398", "prediction": "The answer is: 2008"}
{"question_id": "single_nq_dev_6265", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_319", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5917", "prediction": "So the answer is: Bombay."}
{"question_id": "single_nq_dev_2012", "prediction": "So the answer is: Jefferson Davis"}
{"question_id": "single_nq_dev_2670", "prediction": "[i]"}
{"question_id": "single_nq_dev_1098", "prediction": "The 55th person is Ralph Maria Siegel and Ernst Neubach. So the answer is: Ralph Maria Siegel and Ernst Neubach."}
{"question_id": "single_nq_dev_1383", "prediction": "So the answer is: Chief Taoyateduta (aka, Little Crow)."}
{"question_id": "single_nq_dev_4884", "prediction": "So the answer is: the Supplemental Nutrition Assistance Program (SNAP)"}
{"question_id": "single_nq_dev_609", "prediction": "So the answer is: James Hargreaves"}
{"question_id": "single_nq_dev_3938", "prediction": "the Bee Gees"}
{"question_id": "single_nq_dev_2852", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_1370", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2528", "prediction": "[i]"}
{"question_id": "single_nq_dev_6301", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4234", "prediction": "[i]"}
{"question_id": "single_nq_dev_4564", "prediction": "[a]."}
{"question_id": "single_nq_dev_5406", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_1019", "prediction": "The Executive Residence."}
{"question_id": "single_nq_dev_2742", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_5401", "prediction": "The answer is: (i)."}
{"question_id": "single_nq_dev_3246", "prediction": "the answer is: extended"}
{"question_id": "single_nq_dev_3876", "prediction": "So the answer is: Ukraine holds decisive power regarding Antonov's fate through governmental approval processes"}
{"question_id": "single_nq_dev_6295", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_4389", "prediction": "the answer is: 22 March 1963"}
{"question_id": "single_nq_dev_4500", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_871", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_4347", "prediction": "So the answer is: [ii]"}
{"question_id": "single_nq_dev_5568", "prediction": "So the answer is: lyricist, filmmaker multiple producers on this one."}
{"question_id": "single_nq_dev_4350", "prediction": "So the answer is: Audrey II."}
{"question_id": "single_nq_dev_2966", "prediction": "The clone wars was a science fiction television series that aired on the SciFi Channel. Count Dooku was voiced by Corey Burton."}
{"question_id": "single_nq_dev_1783", "prediction": "[i]"}
{"question_id": "single_nq_dev_2498", "prediction": "[i]"}
{"question_id": "single_nq_dev_4596", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3039", "prediction": "So the answer is: [a]."}
{"question_id": "single_nq_dev_2409", "prediction": "[i]"}
{"question_id": "single_nq_dev_5815", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_6337", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3296", "prediction": "So the answer is: Alsace-Lorraine"}
{"question_id": "single_nq_dev_3221", "prediction": "the answer is: (ii)."}
{"question_id": "single_nq_dev_4304", "prediction": "The Bachelorette"}
{"question_id": "single_nq_dev_659", "prediction": "So the answer is: Summer Hartley"}
{"question_id": "single_nq_dev_4439", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_1584", "prediction": "[i]"}
{"question_id": "single_nq_dev_1620", "prediction": "So the answer is: Shane Black"}
{"question_id": "single_nq_dev_5269", "prediction": "So the answer is: Mohanlal"}
{"question_id": "single_nq_dev_5054", "prediction": "the answer is: 1987"}
{"question_id": "single_nq_dev_5813", "prediction": "The answer is: [I]"}
{"question_id": "single_nq_dev_2701", "prediction": "The spies fire at Gulliver from a cliff, but Prince David diverts the shot and falls to his Elizabeth II; the back featured a meadow scene with two robins. So the answer is: Prince David."}
{"question_id": "single_nq_dev_3887", "prediction": "So the answer is: [I] during the 1980s."}
{"question_id": "single_nq_dev_5420", "prediction": "So the answer is: Bernard Herrmann."}
{"question_id": "single_nq_dev_683", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4878", "prediction": "the answer is: August 16 , 1999"}
{"question_id": "single_nq_dev_4004", "prediction": "So the answer is: 2nd century BC Hipparchus"}
{"question_id": "single_nq_dev_4748", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2862", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2934", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_3427", "prediction": "The king of England that abdicated was Edward VIII. So the answer is: Edward VIII."}
{"question_id": "single_nq_dev_2749", "prediction": "So the answer is: Bill Murray"}
{"question_id": "single_nq_dev_1821", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_802", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_1949", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_3722", "prediction": "the answer is: None"}
{"question_id": "single_nq_dev_4982", "prediction": "The African nation Wakanda"}
{"question_id": "single_nq_dev_6434", "prediction": "The score by composer-songwriter Marc Shaiman"}
{"question_id": "single_nq_dev_4170", "prediction": "the answer is: 1)."}
{"question_id": "single_nq_dev_3428", "prediction": "[i]"}
{"question_id": "single_nq_dev_1050", "prediction": "So the answer is: Iverson Movie Ranch"}
{"question_id": "single_nq_dev_599", "prediction": "the answer is: Trixie Mattel"}
{"question_id": "single_nq_dev_931", "prediction": "the answer is: Melanie Paxson"}
{"question_id": "single_nq_dev_2625", "prediction": "So the answer is: [B]."}
{"question_id": "single_nq_dev_1045", "prediction": "So the answer is: i."}
{"question_id": "single_nq_dev_5939", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_5646", "prediction": "The answer is: No"}
{"question_id": "single_nq_dev_1277", "prediction": "So the answer is: Stuart Hamblen"}
{"question_id": "single_nq_dev_5773", "prediction": "So the answer is: Paul Simon."}
{"question_id": "single_nq_dev_4652", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_799", "prediction": "the answer is: b)."}
{"question_id": "single_nq_dev_6160", "prediction": "[i]"}
{"question_id": "single_nq_dev_4923", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_3820", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1004", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_115", "prediction": "So the answer is: 1990"}
{"question_id": "single_nq_dev_4294", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_2982", "prediction": "the Third-Cause Fallacy"}
{"question_id": "single_nq_dev_4618", "prediction": "derived from 'Les Habitants'"}
{"question_id": "single_nq_dev_4536", "prediction": "The Flash starred John Wesley Shipp as Allen, along with Amanda Pays, and Alex Désert. So the answer is John Wesley Shipp."}
{"question_id": "single_nq_dev_360", "prediction": "So the answer is: Midway."}
{"question_id": "single_nq_dev_3454", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5768", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_2273", "prediction": "So the answer is: 154"}
{"question_id": "single_nq_dev_4967", "prediction": "the answer is: John Rawls"}
{"question_id": "single_nq_dev_1271", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_6119", "prediction": "the answer is: no"}
{"question_id": "single_nq_dev_5204", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4862", "prediction": "the answer is: West Indies"}
{"question_id": "single_nq_dev_3252", "prediction": "the answer is: Candice Brown"}
{"question_id": "single_nq_dev_1989", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_5555", "prediction": "the answer is: Ernie Isley"}
{"question_id": "single_nq_dev_5336", "prediction": "the answer is: 1)."}
{"question_id": "single_nq_dev_3098", "prediction": "The actor who plays Brooks in the movie Game Night is Goulet."}
{"question_id": "single_nq_dev_3061", "prediction": "[i]"}
{"question_id": "single_nq_dev_1812", "prediction": "Asha Bhosle"}
{"question_id": "single_nq_dev_5816", "prediction": "the answer is: None"}
{"question_id": "single_nq_dev_2428", "prediction": "Barry Island"}
{"question_id": "single_nq_dev_1730", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4974", "prediction": "the two major lineages that make up the living amniota are synapsids and the sauropsids"}
{"question_id": "single_nq_dev_4743", "prediction": "So the answer is: Aretha Franklin"}
{"question_id": "single_nq_dev_4921", "prediction": "So the answer is: November 26, 1989"}
{"question_id": "single_nq_dev_1207", "prediction": "[i]"}
{"question_id": "single_nq_dev_3682", "prediction": "The Brothers Grimm published their first volume of collected fairy tales in 1812. So the answer is: 1812."}
{"question_id": "single_nq_dev_196", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_5817", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_3122", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_3726", "prediction": "the answer is: Max von Sydow"}
{"question_id": "single_nq_dev_2376", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_1789", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_1035", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1015", "prediction": "So the answer is: Manchester United"}
{"question_id": "single_nq_dev_5173", "prediction": "So the answer is: Mahatma Gandhi"}
{"question_id": "single_nq_dev_4052", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5210", "prediction": "So the answer is: [B]."}
{"question_id": "single_nq_dev_1931", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3415", "prediction": "So the answer is: [b]."}
{"question_id": "single_nq_dev_3245", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_987", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_67", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_3440", "prediction": "the answer is: [a]."}
{"question_id": "single_nq_dev_3363", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_1892", "prediction": "the answer is: FedEx"}
{"question_id": "single_nq_dev_4960", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_1514", "prediction": "(A)."}
{"question_id": "single_nq_dev_590", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_6423", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_33", "prediction": "The West Indies"}
{"question_id": "single_nq_dev_1545", "prediction": "the transition from preindustrial society to industrial society was brought about by [i]"}
{"question_id": "single_nq_dev_1903", "prediction": "the answer is: Evvie McKinney"}
{"question_id": "single_nq_dev_5712", "prediction": "So the answer is: [ii]"}
{"question_id": "single_nq_dev_4121", "prediction": "The answer is: unanswerable."}
{"question_id": "single_nq_dev_1316", "prediction": "the answer is: continental and oceanic crust"}
{"question_id": "single_nq_dev_1318", "prediction": "the answer is: Nepal"}
{"question_id": "single_nq_dev_2249", "prediction": "the answer is: No"}
{"question_id": "single_nq_dev_44", "prediction": "the answer is: Roger Miller"}
{"question_id": "single_nq_dev_2335", "prediction": "[II]"}
{"question_id": "single_nq_dev_4237", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_5694", "prediction": "the answer is: Daniel Boone"}
{"question_id": "single_nq_dev_3106", "prediction": "The answer is: [I]"}
{"question_id": "single_nq_dev_2499", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_6446", "prediction": "The Beatles"}
{"question_id": "single_nq_dev_296", "prediction": "[i]"}
{"question_id": "single_nq_dev_79", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_2306", "prediction": "The plot of The Bodyguard is about a bodyguard who is hired by a wealthy client to protect him from a ruthless ex-con. Eddie Orlofsky is the character who tried to kill Rachel. So the answer is: Eddie Orlofsky."}
{"question_id": "single_nq_dev_5758", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_6506", "prediction": "So the answer is: Kissy Suzuki"}
{"question_id": "single_nq_dev_6043", "prediction": "So the answer is: [a]."}
{"question_id": "single_nq_dev_1593", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_5909", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_5392", "prediction": "So the answer is: None"}
{"question_id": "single_nq_dev_4735", "prediction": "So the answer is: 1871"}
{"question_id": "single_nq_dev_3125", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1854", "prediction": "The number of episodes in season 8 of Walking Dead is 16 episodes."}
{"question_id": "single_nq_dev_4649", "prediction": "[a]."}
{"question_id": "single_nq_dev_684", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_6109", "prediction": "[i]"}
{"question_id": "single_nq_dev_3660", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5250", "prediction": "[i]"}
{"question_id": "single_nq_dev_4012", "prediction": "So the answer is: 128 symbols"}
{"question_id": "single_nq_dev_5147", "prediction": "the answer is: Pain Perdu"}
{"question_id": "single_nq_dev_4463", "prediction": "The San Diego Zoo Safari Park, originally named the San Diego Wild Animal Park until 2010, is an 1,800 acre (730 ha) zoo in the San Pasqual Valley area of San Diego, California, near Escondido. So the answer is: 1,800 acre (730 ha)."}
{"question_id": "single_nq_dev_1334", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_5276", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1323", "prediction": "The corresponding node of the Graph Answer is (II)."}
{"question_id": "single_nq_dev_4229", "prediction": "the answer is: In 2011, contestant Amelia Lily performed the song on series 8 of UK's \"The X Factor\"."}
{"question_id": "single_nq_dev_2536", "prediction": "So the answer is: intracellular fluid"}
{"question_id": "single_nq_dev_6198", "prediction": "the answer is: No."}
{"question_id": "single_nq_dev_5888", "prediction": "the answer is: Nicéphore Niépce"}
{"question_id": "single_nq_dev_783", "prediction": "So the answer is: 634."}
{"question_id": "single_nq_dev_6342", "prediction": "the answer is: Xi Jinping"}
{"question_id": "single_nq_dev_3554", "prediction": "the minister is Harsh Vardhan"}
{"question_id": "single_nq_dev_3616", "prediction": "So the answer is: the irritating folk singer"}
{"question_id": "single_nq_dev_1191", "prediction": "So the answer is: Great Plains"}
{"question_id": "single_nq_dev_5244", "prediction": "So the answer is: 1989"}
{"question_id": "single_nq_dev_3532", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_2617", "prediction": "the surgeon Dr Kenyon Welch"}
{"question_id": "single_nq_dev_4969", "prediction": "the final answer is: Lori McKenna"}
{"question_id": "single_nq_dev_4179", "prediction": "So the answer is: state and federal"}
{"question_id": "single_nq_dev_3906", "prediction": "The Flash premiered in North America on October 7, 2014, where the pilot became the second-most watched premiere him, though. So the answer is October 7 , 2014."}
{"question_id": "single_nq_dev_5930", "prediction": "The main character in Shadow of War is Talion."}
{"question_id": "single_nq_dev_614", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_4567", "prediction": "The Beatles"}
{"question_id": "single_nq_dev_5021", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3283", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4879", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_4674", "prediction": "The captain. The captain introduced the term in group and out group. Therefore, the final answer is the captain."}
{"question_id": "single_nq_dev_5087", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_6317", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_417", "prediction": "The Stanley Hotel is a hotel in Estes Park. The Stanley Hotel is in Estes Park. The Stanley Hotel was filmed in Estes Park. The Stanley Hotel is a [i] hotel. The Stanley Hotel is a [2]. The Stanley Hotel is a [i] hotel."}
{"question_id": "single_nq_dev_3971", "prediction": "Taylor also had a small role in John Milius' \"Conan the Barbarian\", which was filmed in Francucci."}
{"question_id": "single_nq_dev_2080", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4224", "prediction": "The answer is: Kaushal Manda"}
{"question_id": "single_nq_dev_5908", "prediction": "the answer is: KOD"}
{"question_id": "single_nq_dev_1317", "prediction": "the answer is: i."}
{"question_id": "single_nq_dev_1527", "prediction": "the answer is: National Flag in its premises"}
{"question_id": "single_nq_dev_6253", "prediction": "So the answer is: McConachy"}
{"question_id": "single_nq_dev_5453", "prediction": "the answer is: Ed Sheeran & Nancy Mulligan"}
{"question_id": "single_nq_dev_3748", "prediction": "So the answer is: judges"}
{"question_id": "single_nq_dev_2014", "prediction": "The music was composed by Philippe Gentil and the lyrics were written by Jean-Georges Prosper. So the answer is Philippe Gentil."}
{"question_id": "single_nq_dev_1292", "prediction": "So the answer is: (i)."}
{"question_id": "single_nq_dev_1686", "prediction": "the answer is: 2002"}
{"question_id": "single_nq_dev_808", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_3549", "prediction": "the answer is: between oceanic plates and exist as mid-oceanic ridges"}
{"question_id": "single_nq_dev_4854", "prediction": "the answer is: 2008"}
{"question_id": "single_nq_dev_4132", "prediction": "The number of episodes in season 2 of The Crown is 13 episodes."}
{"question_id": "single_nq_dev_3263", "prediction": "the answer is: James Hargreaves"}
{"question_id": "single_nq_dev_321", "prediction": "The South Carolina Gamecocks."}
{"question_id": "single_nq_dev_744", "prediction": "the answer is: Lou Reed"}
{"question_id": "single_nq_dev_5833", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1720", "prediction": "the answer is: [ii]"}
{"question_id": "single_nq_dev_2941", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_55", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4392", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_6403", "prediction": "So the answer is: [II]"}
{"question_id": "single_nq_dev_3519", "prediction": "So the answer is: (ii)."}
{"question_id": "single_nq_dev_2994", "prediction": "So the answer is: Curling"}
{"question_id": "single_nq_dev_72", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5715", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_1336", "prediction": "So the answer is: pleuritic chest pain"}
{"question_id": "single_nq_dev_2569", "prediction": "the answer is: Merry Clayton"}
{"question_id": "single_nq_dev_934", "prediction": "So the answer is: no"}
{"question_id": "single_nq_dev_4635", "prediction": "The unanswerable answer is Dorothy Dwan."}
{"question_id": "single_nq_dev_3042", "prediction": "the answer is: unanswerable"}
{"question_id": "single_nq_dev_5217", "prediction": "the answer is: Eddie Orlofsky"}
{"question_id": "single_nq_dev_1064", "prediction": "the transitional epithelium"}
{"question_id": "single_nq_dev_5722", "prediction": "The Return of the King"}
{"question_id": "single_nq_dev_4255", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2286", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5877", "prediction": "So the answer is: issues related to political corruption tied to alcohol consumption"}
{"question_id": "single_nq_dev_6081", "prediction": "the answer is: April 8 , 2018."}
{"question_id": "single_nq_dev_2541", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_2448", "prediction": "So the answer is: (a)."}
{"question_id": "single_nq_dev_1057", "prediction": "The four-eyed fishes are a genus, Anableps, of fishes in the family Anablepidae. So the answer is: Anableps."}
{"question_id": "single_nq_dev_2322", "prediction": "the answer is: unanswerable"}
{"question_id": "single_nq_dev_2387", "prediction": "[i]"}
{"question_id": "single_nq_dev_4590", "prediction": "the Kingdom of Great Britain"}
{"question_id": "single_nq_dev_2816", "prediction": "the answer is: Gayla Peevey"}
{"question_id": "single_nq_dev_6333", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_6233", "prediction": "So the answer is: American country music singer Craig Morgan"}
{"question_id": "single_nq_dev_2775", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_1814", "prediction": "the answer is: [II]"}
{"question_id": "single_nq_dev_5069", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1328", "prediction": "So the answer is: Ahmad Shah Durrani"}
{"question_id": "single_nq_dev_6251", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_831", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_194", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_3213", "prediction": "the Portuguese"}
{"question_id": "single_nq_dev_6452", "prediction": "So the answer is: i."}
{"question_id": "single_nq_dev_1796", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_4568", "prediction": "So the answer is: [II]"}
{"question_id": "single_nq_dev_2241", "prediction": "(ii)."}
{"question_id": "single_nq_dev_1140", "prediction": "the answer is: (II)"}
{"question_id": "single_nq_dev_1276", "prediction": "[i]"}
{"question_id": "single_nq_dev_1386", "prediction": "So the answer is: Dean."}
{"question_id": "single_nq_dev_2117", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_1633", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1723", "prediction": "the answer is: [B]."}
{"question_id": "single_nq_dev_6474", "prediction": "the answer is: Adam Schlesinger"}
{"question_id": "single_nq_dev_325", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3624", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_4975", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_2339", "prediction": "the answer is: Population bottleneck"}
{"question_id": "single_nq_dev_3016", "prediction": "The answer is: [B]."}
{"question_id": "single_nq_dev_5988", "prediction": "the answer is: no"}
{"question_id": "single_nq_dev_4776", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5678", "prediction": "The inscription on the statue of liberty was written by Emma Lazarus. William Maxwell Evarts is the 364th writer. So the answer is: Emma Lazarus."}
{"question_id": "single_nq_dev_2656", "prediction": "the answer is: Louis Hynes"}
{"question_id": "single_nq_dev_2020", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_1733", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_2188", "prediction": "So the answer is: Kimbra."}
{"question_id": "single_nq_dev_1138", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_312", "prediction": "the answer is: [ii]"}
{"question_id": "single_nq_dev_2933", "prediction": "[i]"}
{"question_id": "single_nq_dev_4189", "prediction": "the answer is: No"}
{"question_id": "single_nq_dev_753", "prediction": "So the answer is: unanswerable."}
{"question_id": "single_nq_dev_474", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_5523", "prediction": "So the answer is: 4"}
{"question_id": "single_nq_dev_6145", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_6483", "prediction": "the heir apparent is her eldest son, Charles, Prince of Wales."}
{"question_id": "single_nq_dev_1883", "prediction": "So the answer is: [ii]"}
{"question_id": "single_nq_dev_4532", "prediction": "So the answer is: king crab"}
{"question_id": "single_nq_dev_6331", "prediction": "The pass laws were repealed by the Identification Act, 1986 and the influx control laws by the Abolition of Influx Control Act, 1986. So the answer is: 1986."}
{"question_id": "single_nq_dev_222", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2641", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4772", "prediction": "So the answer is: Sikh community volunteers"}
{"question_id": "single_nq_dev_4587", "prediction": "The relevant information to answer the above question is: A sequel, \"Guardians of the Galaxy Vol. 2\", was released on May 5, 2017. So the answer is: May 5 , 2017."}
{"question_id": "single_nq_dev_3801", "prediction": "So the answer is: [2]."}
{"question_id": "single_nq_dev_3702", "prediction": "the answer is: Paul Simon"}
{"question_id": "single_nq_dev_3819", "prediction": "the answer is: Andy Serkis"}
{"question_id": "single_nq_dev_5038", "prediction": "the answer is: Royal Cheese"}
{"question_id": "single_nq_dev_1009", "prediction": "So the answer is: Ed Sheeran"}
{"question_id": "single_nq_dev_924", "prediction": "The Beatles"}
{"question_id": "single_nq_dev_4355", "prediction": "So the answer is: Vancouver, British Columbia, Canada"}
{"question_id": "single_nq_dev_3865", "prediction": "the answer is: 1808"}
{"question_id": "single_nq_dev_3010", "prediction": "the answer is: King T'Chaka."}
{"question_id": "single_nq_dev_3260", "prediction": "the answer is: unanswerable"}
{"question_id": "single_nq_dev_2372", "prediction": "the answer is: Austria"}
{"question_id": "single_nq_dev_2683", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1650", "prediction": "So the answer is: international treaties"}
{"question_id": "single_nq_dev_2469", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_69", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_2605", "prediction": "So the answer is: luteinizing hormone (LH) and follicle-stimulating hormone (FSH)"}
{"question_id": "single_nq_dev_4453", "prediction": "the answer is: Barry Nelson"}
{"question_id": "single_nq_dev_532", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_2876", "prediction": "The Night the Lights Went Out in Georgia"}
{"question_id": "single_nq_dev_966", "prediction": "the French Revolution"}
{"question_id": "single_nq_dev_2523", "prediction": "So the answer is: (i)."}
{"question_id": "single_nq_dev_1977", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_6381", "prediction": "the answer is: Peyton"}
{"question_id": "single_nq_dev_3461", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_810", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_562", "prediction": "the Miracle on Ice in 1980"}
{"question_id": "single_nq_dev_4814", "prediction": "So the answer is: (i)."}
{"question_id": "single_nq_dev_3997", "prediction": "So the answer is: Tatyana Ali and Alfonso Ribeiro"}
{"question_id": "single_nq_dev_5017", "prediction": "The answer is: (I)."}
{"question_id": "single_nq_dev_4156", "prediction": "So the answer is: most types of eukaryotic cells, but is absent from red blood cells and spermatozoa."}
{"question_id": "single_nq_dev_4165", "prediction": "So the answer is: September 2011."}
{"question_id": "single_nq_dev_1547", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_2782", "prediction": "the final answer is: 1919"}
{"question_id": "single_nq_dev_3879", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_1243", "prediction": "the answer is: Jim Meskimen"}
{"question_id": "single_nq_dev_1339", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5974", "prediction": "the answer is: 1860"}
{"question_id": "single_nq_dev_1100", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_4400", "prediction": "So the answer is: polypeptide chains from amino acids"}
{"question_id": "single_nq_dev_2897", "prediction": "the answer is: Lee Adams"}
{"question_id": "single_nq_dev_3342", "prediction": "So the answer is: Victory Gardens."}
{"question_id": "single_nq_dev_2967", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5606", "prediction": "the answer is: Dave Dudley"}
{"question_id": "single_nq_dev_2863", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_6324", "prediction": "[i]"}
{"question_id": "single_nq_dev_1314", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_4301", "prediction": "The Illustrator of Winnie the Pooh is E. H. Shepard"}
{"question_id": "single_nq_dev_3649", "prediction": "The Stellar Awards"}
{"question_id": "single_nq_dev_1790", "prediction": "[i]"}
{"question_id": "single_nq_dev_4436", "prediction": "the cell division process responsible for the formation of gametes is meiosis"}
{"question_id": "single_nq_dev_2784", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_5936", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1440", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_1525", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_3486", "prediction": "the answer is: No"}
{"question_id": "single_nq_dev_989", "prediction": "unanswerable"}
{"question_id": "single_nq_dev_3874", "prediction": "So the answer is: Jeffrey Hunter"}
{"question_id": "single_nq_dev_4287", "prediction": "So the answer is: United Nations General Assembly"}
{"question_id": "single_nq_dev_5713", "prediction": "the answer is: No"}
{"question_id": "single_nq_dev_1763", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_5272", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_4811", "prediction": "The answer is: no."}
{"question_id": "single_nq_dev_1938", "prediction": "the answer is: (i)"}
{"question_id": "single_nq_dev_4883", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_2202", "prediction": "The state with in god we trust on their license plate is Florida."}
{"question_id": "single_nq_dev_3373", "prediction": "the answer is: Christopher Columbus"}
{"question_id": "single_nq_dev_5191", "prediction": "the answer is: November 2001"}
{"question_id": "single_nq_dev_268", "prediction": "So the answer is: Imperial Japanese Navy Air Service"}
{"question_id": "single_nq_dev_5748", "prediction": "unanswerable"}
{"question_id": "single_nq_dev_3473", "prediction": "the answer is: [ii]"}
{"question_id": "single_nq_dev_451", "prediction": "So the answer is: i."}
{"question_id": "single_nq_dev_950", "prediction": "The smallest number of digits in a set is [i] and the largest number is [ii] So the answer is: [ii]."}
{"question_id": "single_nq_dev_4570", "prediction": "the answer is: British East India Company"}
{"question_id": "single_nq_dev_4115", "prediction": "So the answer is: [I]"}
{"question_id": "single_nq_dev_5980", "prediction": "the answer is: Julia Stiles"}
{"question_id": "single_nq_dev_1268", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_229", "prediction": "The New International Version (NIV) is an English translation of the Bible first published in 1978 by Biblica (formerly the International Bible Society). So the answer is: 1978."}
{"question_id": "single_nq_dev_2142", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_3633", "prediction": "the answer is: Australia"}
{"question_id": "single_nq_dev_2091", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_3155", "prediction": "So the answer is: no"}
{"question_id": "single_nq_dev_5565", "prediction": "the answer is: Jason Donovan"}
{"question_id": "single_nq_dev_1936", "prediction": "The answer is: [i]"}
{"question_id": "single_nq_dev_817", "prediction": "The force that acts on an object in free fall is gravity."}
{"question_id": "single_nq_dev_4380", "prediction": "the answer is: gonadotropes."}
{"question_id": "single_nq_dev_2370", "prediction": "the answer is: [I]"}
{"question_id": "single_nq_dev_86", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_2886", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_6399", "prediction": "The Young and the Restless"}
{"question_id": "single_nq_dev_5206", "prediction": "So the answer is: [a]."}
{"question_id": "single_nq_dev_4314", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_313", "prediction": "So the answer is: 40."}
{"question_id": "single_nq_dev_2610", "prediction": "So the answer is: The Book of Joshua"}
{"question_id": "single_nq_dev_2580", "prediction": "the answer is: nine"}
{"question_id": "single_nq_dev_2612", "prediction": "[i]"}
{"question_id": "single_nq_dev_6221", "prediction": "So the answer is: Kepler"}
{"question_id": "single_nq_dev_2185", "prediction": "So the answer is: c)."}
{"question_id": "single_nq_dev_849", "prediction": "The town Anguillara Sabazia outside of Rome."}
{"question_id": "single_nq_dev_151", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_551", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4742", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3202", "prediction": "So the answer is: Anguillara Sabazia"}
{"question_id": "single_nq_dev_6362", "prediction": "the answer is: 1957"}
{"question_id": "single_nq_dev_4987", "prediction": "unanswerable"}
{"question_id": "single_nq_dev_772", "prediction": "So the answer is: no."}
{"question_id": "single_nq_dev_334", "prediction": "So the answer is: At the beginning of Season 8"}
{"question_id": "single_nq_dev_5861", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1963", "prediction": "the answer is: 1983"}
{"question_id": "single_nq_dev_4244", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5065", "prediction": "[b]."}
{"question_id": "single_nq_dev_3201", "prediction": "[1]."}
{"question_id": "single_nq_dev_244", "prediction": "the answer is: None"}
{"question_id": "single_nq_dev_2797", "prediction": "So the answer is: misophonia"}
{"question_id": "single_nq_dev_4825", "prediction": "So the answer is: Kanpur"}
{"question_id": "single_nq_dev_5807", "prediction": "[i]"}
{"question_id": "single_nq_dev_1152", "prediction": "The answer is: [ii]"}
{"question_id": "single_nq_dev_4230", "prediction": "So the answer is: no."}
{"question_id": "single_nq_dev_723", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_9", "prediction": "The answer is: Queen Hippolyta."}
{"question_id": "single_nq_dev_5255", "prediction": "The convent of the mission de San Jose is in Fremont, California."}
{"question_id": "single_nq_dev_2342", "prediction": "So the answer is: Luke"}
{"question_id": "single_nq_dev_5783", "prediction": "The answer is: No."}
{"question_id": "single_nq_dev_2292", "prediction": "So the answer is: JetBlue Airways and Hawaiian Airlines"}
{"question_id": "single_nq_dev_5292", "prediction": "The boy in A Bronx Tale is played by Brancato."}
{"question_id": "single_nq_dev_5832", "prediction": "So the answer is: unanswerable."}
{"question_id": "single_nq_dev_6409", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2665", "prediction": "So the answer is: his real-life grandmother"}
{"question_id": "single_nq_dev_2248", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2348", "prediction": "The Hubble Space Telescope was launched on April 24, 1990 by the Space Shuttle \"Discovery\" (STS-31)"}
{"question_id": "single_nq_dev_576", "prediction": "So the answer is: [II]"}
{"question_id": "single_nq_dev_5860", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_4379", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_1273", "prediction": "the answer is: 11"}
{"question_id": "single_nq_dev_497", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4315", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_2385", "prediction": "the answer is: [ii]"}
{"question_id": "single_nq_dev_6436", "prediction": "[i]"}
{"question_id": "single_nq_dev_2169", "prediction": "So the answer is: Antonie van Leeuwenhoek"}
{"question_id": "single_nq_dev_3645", "prediction": "the answer is: La Poudre Pass Lake"}
{"question_id": "single_nq_dev_3344", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_777", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_4329", "prediction": "the answer is: [i]"}
{"question_id": "single_nq_dev_3700", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_618", "prediction": "the answer is: No"}
{"question_id": "single_nq_dev_1672", "prediction": "So the answer is: No"}
{"question_id": "single_nq_dev_4498", "prediction": "The song You Got Me Going In Circles was written by Jerry Peters and Anita Poree."}
{"question_id": "single_nq_dev_514", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3568", "prediction": "So the answer is: unanswerable"}
{"question_id": "single_nq_dev_860", "prediction": "The Imperial Valley relies heavily on water sourced from the Colorado River via the All-American Canal for agricultural purposes and other needs."}
{"question_id": "single_nq_dev_329", "prediction": "So the answer is: ten"}
{"question_id": "single_nq_dev_2753", "prediction": "So the answer is: No."}
{"question_id": "single_nq_dev_3845", "prediction": "So the answer is: (i)."}
{"question_id": "single_nq_dev_2781", "prediction": "the pen name of American author Daniel Handler"}
{"question_id": "single_nq_dev_1114", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_3791", "prediction": "The answer is: Victoria"}
{"question_id": "single_nq_dev_3313", "prediction": "So the answer is: [i]"}
{"question_id": "single_nq_dev_5338", "prediction": "The number of digits in the digit 167 is 0 and the number of digits in the digit 167 is 1. So the answer is: 167."}
{"question_id": "single_nq_dev_3035", "prediction": "So the answer is: no"}
{"question_id": "single_nq_dev_5439", "prediction": "So the answer is: United States Army Air Forces"}
{"question_id": "single_nq_dev_3286", "prediction": "the answer is: Jeremy Renner"}
{"question_id": "single_nq_dev_1719", "prediction": "[i]"}
{"question_id": "single_nq_dev_5096", "prediction": "So the answer is: Billy Preston and Bruce Fisher"}
{"question_id": "single_nq_dev_2369", "prediction": "So the answer is: [B]."}
{"question_id": "single_nq_dev_2786", "prediction": "the answer is: Han Ye-ri, Han Seung-yeon, Park Eun-bin, Ryu Hwa-young and Park Hye-soo"}
{"question_id": "single_nq_dev_3838", "prediction": "So the answer is: in the uppermost solid layer of our planet known as the **Earth's Lithosphere**"}
