#!/usr/bin/env python3
"""
测试硅基流动配置的简单脚本
"""

from config import RAGConfig
from graph_rag.langchain_wrapper import CustomLLM_LangChain
from graph_rag.llamaindex_wrapper import CustomLLM
from llama_index.core.llms import ChatMessage

def test_config():
    """测试配置是否正确加载"""
    config = RAGConfig()
    
    print("=== 配置信息 ===")
    print(f"向量检索URL: {config.llm_vec_url}")
    print(f"向量检索模型: {config.llm_vec_model}")
    print(f"详细检索URL: {config.llm_detail_url}")
    print(f"详细检索模型: {config.llm_detail_model}")
    print(f"嵌入模型路径: {config.embedding_model}")
    print(f"向量存储路径: {config.vector_store_path}")
    print(f"图数据库URL: {config.graph_url}")
    print()
    
    return config

def test_langchain_llm(config):
    """测试LangChain包装器"""
    print("=== 测试LangChain LLM ===")
    try:
        llm_vec = CustomLLM_LangChain(
            model_name=config.llm_vec_model,
            base_url=config.llm_vec_url,
            temperature=0.5,
            max_tokens=100,
            presence_penalty=1.03,
            frequency_penalty=1.0,
            top_p=0.95,
            streaming=False,
            openai_api_key=config.llm_vec_api_key
        )
        
        # 简单测试
        response = llm_vec._call("Hello, how are you?")
        print(f"LangChain LLM 响应: {response[:100]}...")
        print("✅ LangChain LLM 测试成功")
        
    except Exception as e:
        print(f"❌ LangChain LLM 测试失败: {e}")
    
    print()

def test_llamaindex_llm(config):
    """测试LlamaIndex包装器"""
    print("=== 测试LlamaIndex LLM ===")
    try:
        llm = CustomLLM(
            base_url=config.llm_detail_url,
            model=config.llm_detail_model,
            max_tokens=100,
            presence_penalty=1.03,
            frequency_penalty=1.0,
            temperature=0.5,
            top_p=0.92,
            streaming=False,
            api_key=config.llm_detail_api_key,
        )
        
        # 简单测试
        response = llm.complete("Hello, how are you?")
        print(f"LlamaIndex LLM 响应: {response.text[:100]}...")
        print("✅ LlamaIndex LLM 测试成功")
        
    except Exception as e:
        print(f"❌ LlamaIndex LLM 测试失败: {e}")
    
    print()

def main():
    """主函数"""
    print("开始测试硅基流动配置...")
    print()
    
    # 测试配置加载
    config = test_config()
    
    # 测试LLM
    test_langchain_llm(config)
    test_llamaindex_llm(config)
    
    print("测试完成！")

if __name__ == "__main__":
    main()
