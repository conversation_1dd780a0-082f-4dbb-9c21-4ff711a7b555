from langchain.embeddings.huggingface import HuggingFaceEmbeddings
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA
from graph_rag.langchain_wrapper import CustomLLM_LangChain,CustomQwenLLM
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.chains import <PERSON><PERSON>hai<PERSON>
from langchain_core.messages import HumanMessage, SystemMessage

import networkx as nx  # 用于图的构建和操作
from llama_index.graph_stores.neo4j import Neo4jPropertyGraphStore
from llama_index.core import PropertyGraphIndex
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from graph_rag.GraphRAGQueryEngine import GraphRAGQueryEngine 
from graph_rag.GraphRAGStore import GraphRAGStore
from graph_rag.llamaindex_wrapper import CustomLLM,QwenLLM
import concurrent.futures
import json
import os
import time
def main():
    # llm_vec = CustomLLM_LangChain(
    # model_name="flan-t5-xl",
    # base_url="http://192.168.224.43:8421",
    # temperature=0.5,
    # max_tokens=2048,
    # presence_penalty=1.03,
    # frequency_penalty=1.0,
    # top_p=0.95,
    # streaming=False,  # 根据需求设置流式输出
    # )
    # llm = CustomLLM(
    # base_url="http://192.168.224.43:8421",
    # model="flan-t5-xl",
    # max_tokens=2048,
    # presence_penalty=1.03,
    # frequency_penalty=1.0,
    
    # temperature=0.5,
    # top_p=0.95,
    # streaming=False,
    # )
    llm_vec = CustomQwenLLM(
    model_name="qwen-turbo",
    openai_api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
    max_tokens=256,
    temperature=0.3,
    stream=False
)
    llm = QwenLLM(
    api_key="sk-efcf7126cc904ccaa7bd6a50e51192fe",
    model="qwen-turbo-latest",
    max_tokens=2048,
    temperature=0.5,
    top_p=0.95,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    streaming=False
    )
    EMBEDDINGS_MODEL = "G:/PAPER/Experiments/Paper_Exp/bge-base-en-v1.5"
    # 1. 初始化组件
    embeddings = HuggingFaceEmbeddings(
        model_name=EMBEDDINGS_MODEL,
        model_kwargs={"device": "cuda"}
    )
    vectorstore = FAISS.load_local("G:/PAPER/Experiments/Paper_Exp/QA-HybridRAG/vector_index/nq_vectorstore", embeddings, allow_dangerous_deserialization=True)
    
    # 2. 构建检索链
    qa_chain = RetrievalQA.from_chain_type(
        llm=llm_vec,
        chain_type="stuff",
        retriever=vectorstore.as_retriever(search_kwargs={"k": 5})
    )
    
    #知识图检索
    # 重新初始化 graph_store 指向已存储的数据库
    print("[DEBUG] Step 3: Initializing GraphRAGStore...")
    gs_start_time = time.time()
    graph_store = GraphRAGStore(
        username="neo4j", password="ZX20010103", url="bolt://10.1.40.104:7687",
            refresh_schema=False,     # ← 关闭自动刷新
            #create_indexes=False,     # ← 如无新增索引需求，可一并关闭
            # enhanced_schema=False     # ← 关闭深度 schema 分析
    )
    gs_end_time = time.time()
    print(f"[DEBUG] Step 4: Initializing GraphRAGStore Finished in:{gs_end_time - gs_start_time}s")
    # 初始化嵌入模型（这步一般不影响查询，只用于衡量相似度）
    # embed_model = HuggingFaceEmbedding(
    #     model_name=EMBEDDINGS_MODEL, device="cuda"
    # )
    # 此处不用传入节点和kg_extractor，因为知识图已经构建好
    graph_index = PropertyGraphIndex(
        llm=llm,
        nodes=[],  # 或者不传入节点
        kg_extractors=[],  # 不需要抽取新信息
        embed_model=embeddings,
        property_graph_store=graph_store,
        show_progress=False,
    )

    graph_index.property_graph_store.build_communities()
    # 3. 构建图检索引擎
    query_engine = GraphRAGQueryEngine(
        graph_store=graph_index.property_graph_store,
        llm=llm,
        index=graph_index,
        similarity_top_k=10
    )
    
    # 4. 用户查询处理
    user_query = "where does the hanger steak come from on a cow"
    
    # with concurrent.futures.ThreadPoolExecutor() as executor:
    #     future_vector = executor.submit(qa_chain.run, user_query)
    #     future_graph = executor.submit(query_engine.get_all_community_answers, user_query)
    #     vector_answer = future_vector.result()
    #     graph_intermediate_answers = future_graph.result()
    # 同步调用，避免线程中 asyncio.run 导致错误
    vector_answer = qa_chain.run(user_query)
    graph_intermediate_answers = query_engine.get_all_community_answers(user_query)
    # 6. 设计聚合提示词
    aggregation_prompt = f"""
    Combine information from multiple sources to answer the query:
    
    [USER QUERY]
    {user_query}
    
    [VECTOR RETRIEVAL RESULT]
    {vector_answer}
    
    [GRAPH COMMUNITY ANSWERS]
    {json.dumps(graph_intermediate_answers, indent=2)}

    """
    
    # 7. 生成最终答案
    final_answer = llm.complete(aggregation_prompt).text
    print("Final Answer:", final_answer)
    
    # 8. 调试输出
    print("\n[Debug Info]")
    print("Vector Retrieval:", vector_answer)
    print("Graph Communities:", graph_intermediate_answers)
    
if __name__ == "__main__":
    total_time_start = time.time()
    main()  # 确保调用主函数
    total_end_time = time.time()
    print(f"total time:{total_end_time-total_time_start}")
