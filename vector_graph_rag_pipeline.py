from langchain.embeddings.huggingface import HuggingFaceEmbeddings
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA
from graph_rag.langchain_wrapper import CustomLLM_LangChain
from langchain.prompts import ChatPromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain_core.messages import HumanMessage, SystemMessage

import networkx as nx  # 用于图的构建和操作
from llama_index.graph_stores.neo4j import Neo4j<PERSON>ropertyGraphStore
from llama_index.core import PropertyGraphIndex
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from graph_rag.GraphRAGQueryEngine import GraphRAGQueryEngine 
from graph_rag.GraphRAGStore import GraphRAGStore
from graph_rag.llamaindex_wrapper import CustomLLM
import concurrent.futures
import json
def main():
    llm_vec = CustomLLM_LangChain(
    model_name="ds-r1-32b",
    base_url="http://192.168.224.160:1025/v1",
    temperature=0.5,
    max_tokens=2048,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    top_p=0.95,
    streaming=False,  # 根据需求设置流式输出
    openai_api_key=""   # 如无验证则留空
    )
    

    llm = CustomLLM(
    base_url="http://192.168.224.160:1025/v1",
    model="llama_65b",
    max_tokens=1024,
    presence_penalty=1.03,
    frequency_penalty=1.0,
    temperature=0.05,
    top_p=0.92,
    streaming=False,
    )
    EMBEDDINGS_MODEL = "/home/<USER>/bge-base-en-v1.5/"
    # 1. 初始化组件
    embeddings = HuggingFaceEmbeddings(
        model_name=EMBEDDINGS_MODEL,
        model_kwargs={"device": "cpu"}
    )
    vectorstore = FAISS.load_local("/home/<USER>/QA-HybridRAG/vector_index/nq_vectorstore/", embeddings, allow_dangerous_deserialization=True)
    
    # 2. 构建检索链
    qa_chain = RetrievalQA.from_chain_type(
        llm=llm_vec,
        chain_type="stuff",
        retriever=vectorstore.as_retriever(search_kwargs={"k": 5})
    )
    
    #知识图检索
    # 重新初始化 graph_store 指向已存储的数据库
    graph_store = GraphRAGStore(
        username="neo4j", password="ZX20010103", url="bolt://192.168.224.43:7687"
    )
    # 初始化嵌入模型（这步一般不影响查询，只用于衡量相似度）
    embed_model = HuggingFaceEmbedding(
        model_name=EMBEDDINGS_MODEL, device="cpu"
    )
    # 此处不用传入节点和kg_extractor，因为知识图已经构建好
    graph_index = PropertyGraphIndex(
        llm=llm,
        nodes=[],  # 或者不传入节点
        kg_extractors=[],  # 不需要抽取新信息
        embed_model=embed_model,
        property_graph_store=graph_store,
        show_progress=False,
    )

    graph_index.property_graph_store.build_communities()
    # 3. 构建图检索引擎
    query_engine = GraphRAGQueryEngine(
        graph_store=graph_index.property_graph_store,
        llm=llm,
        index=graph_index,
        similarity_top_k=10
    )
    
    # 4. 用户查询处理
    user_query = ""
    
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future_vector = executor.submit(qa_chain.run, user_query)
        future_graph = executor.submit(query_engine.get_all_community_answers, user_query)
        vector_answer = future_vector.result()
        graph_intermediate_answers = future_graph.result()
    
    # 6. 设计聚合提示词
    aggregation_prompt = f"""
    Combine information from multiple sources to answer the query:
    
    [USER QUERY]
    {user_query}
    
    [VECTOR RETRIEVAL RESULT]
    {vector_answer}
    
    [GRAPH COMMUNITY ANSWERS]
    {json.dumps(graph_intermediate_answers, indent=2)}
    
    Rules:
    1. Resolve conflicts giving priority to graph-based answers
    2. Keep technical terms unchanged
    3. Mark [Graph] or [Vector] when citing specific sources
    """
    
    # 7. 生成最终答案
    final_answer = llm.complete(aggregation_prompt).text
    print("Final Answer:", final_answer)
    
    # 8. 调试输出
    print("\n[Debug Info]")
    print("Vector Retrieval:", vector_answer)
    print("Graph Communities:", graph_intermediate_answers)
if __name__ == "__main__":
    main()  # 确保调用主函数
